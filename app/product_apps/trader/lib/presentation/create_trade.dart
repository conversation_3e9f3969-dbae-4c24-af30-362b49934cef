import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:host/host.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent createOrder() {
  return DisplayableComponent(
    title: 'Create Trade',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/Order': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/order/success.json',
                ),
              ],
            });
          return CreateTradeWidget(
            args: (
              symbolCode: 'AUDCAD',
              symbolImageUrl:
                  "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
              digits: 2,
              minLot: 0.1,
              maxLot: 50.0,
              isForex: true,
              assetType: 'forex',
              lotsSteps: 0.1,
              initialTradeType: null,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/Order': [
                MockResponse(
                  code: 400,
                  bodyFilePath: 'resources/mocks/order/failure.json',
                ),
              ],
            });
          return CreateTradeWidget(
            args: (
              symbolCode: 'EURUSD',
              symbolImageUrl:
                  "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
              digits: 2,
              isForex: true,
              minLot: 0.1,
              maxLot: 50.0,
              assetType: 'forex',
              lotsSteps: 0.1,
              initialTradeType: null,
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Loading',
        onTap: () {
          diContainer<MockApiInterceptor>()..reset();
          return CreateTradeWidget(
            args: (
              symbolCode: 'AUDCAD',
              symbolImageUrl:
                  "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
              digits: 2,
              minLot: 0.1,
              maxLot: 50.0,
              isForex: true,
              assetType: 'forex',
              lotsSteps: 0.1,
              initialTradeType: null,
            ),
          );
        },
      ),
    ],
  );
}

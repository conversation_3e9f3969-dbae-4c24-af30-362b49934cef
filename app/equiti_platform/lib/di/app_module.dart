import 'package:clock/clock.dart';
import 'package:customer_support_chat/customer_support_chat.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/mocks/get_office_code_usecase_impl.dart';
import 'package:equiti_secure_storage/equiti_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:preferences/preferences.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:equiti_identity/equiti_identity.dart';

@module
abstract class AppModule {
  @lazySingleton
  @preResolve
  Future<EquitiPreferences> sharedPreferences() => Preferences.create();

  @lazySingleton
  SecureStorage equitiSecureStorage() => EquitiSecureStorage.create();

  @lazySingleton
  CustomerSupportChat customerSupportChat(CustomerChatSupportConfig config) =>
      CustomerSupportChat(config);

  @singleton
  LocaleManager localeModel(EquitiPreferences preferences) =>
      LocaleManager(preferences: preferences);

  @singleton
  ThemeManager themeManager(EquitiPreferences preferences) =>
      ThemeManager(preferences);

  @lazySingleton
  GetOfficeCodeUseCase get getOfficeCodeUseCase => GetOfficeCodeUseCaseImpl();

  @lazySingleton
  Clock get clock => Clock();

  @Named('environment')
  @singleton
  String environment(AppConfig config) => config.env.name;

  @lazySingleton
  UAEPassEnvConfigs uaePassEnvConfigs(AppConfig config) =>
      config.uaePassEnvConfigs;

  @lazySingleton
  IdManager idManager(EquitiPreferences preferences) =>
      EquitiIdManager(preferences: preferences);

  @lazySingleton
  AuthConfig authConfig(AppConfig config) => config.authConfig;

  @lazySingleton
  CustomerChatSupportConfig ccSupprtConfig(AppConfig config) =>
      config.ccSupportConfig;
}

# azure-pipelines/azure-pipelines-build-android-equiti-platform.yaml
name: Build Android Pipeline
trigger: none

parameters:
- name: environment
  displayName: 'Environment'
  type: string
  default: 'development'
  values:
  - development
  - release
  # - staging
  - production
- name: deploy
  displayName: 'Deploy to Firebase App Distribution'
  type: boolean
  default: true

variables:
- name: BASH_ENV
  value: "~/.profile"
- name: DOCKER_REGISTRY_REPO
  value: equiti.flutter
- group: DockerRegistry
- group: FirebaseSecrets
# Environment to app flavor mapping
- name: AppFlavor
  ${{ if eq(parameters.environment, 'development') }}:
    value: 'dev'
  ${{ if eq(parameters.environment, 'staging') }}:
    value: 'stg'
  ${{ if eq(parameters.environment, 'release') }}:
    value: 'rel'
  ${{ if eq(parameters.environment, 'production') }}:
    value: 'prod'

stages:
- stage: 'Build_Android'
  displayName: 'Build Android'
  jobs:
  - job: Build_Android_Job
    pool: 
      name: Mobile-Pipeline-VMSS

    steps:
    - checkout: self
      fetchDepth: 0

    - template: ../templates/initialize-setup-user.yaml
    
    - script: dart pub global run melos bs
      displayName: '<PERSON><PERSON>'

    - script: |
        echo "##vso[task.setvariable variable=ANDROID_SDK_ROOT;]/opt/android-sdk"
        echo "##vso[task.setvariable variable=ANDROID_HOME;]/opt/android-sdk"
        echo "##vso[task.prependpath]/opt/android-sdk/cmdline-tools/latest/bin"
        echo "##vso[task.prependpath]/opt/android-sdk/platform-tools"
        echo "##vso[task.prependpath]/opt/flutter/bin"
        echo "##vso[task.prependpath]/opt/flutter/bin/cache/dart-sdk/bin"
        echo "##vso[task.prependpath]/opt/android-sdk/platforms"
        echo "##vso[task.prependpath]/opt/android-sdk/licenses"
        echo "##vso[task.prependpath]/opt/android-sdk/ndk"
        export ANDROID_SDK_ROOT=/opt/android-sdk
        export PATH=$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/platform-tools:$PATH

      displayName: 'Set Android/Flutter env (persist across steps)'

    - script: flutter doctor -v
      displayName: 'Flutter Doctor'
      env:
        ANDROID_SDK_ROOT: $(ANDROID_SDK_ROOT)
        ANDROID_HOME: $(ANDROID_HOME)

    - template: ../templates/build-android.yaml
      parameters:
        buildType: 'release'
        flavor: $(AppFlavor)
        outputFormat: 'apk'
        outputDirectory: 'build/app/outputs'
        verbose: false
        appPath: 'app/equiti_platform'
        configFilePath: '../../.env/config.$(AppFlavor).json'

- stage: 'Deploy_Android'
  displayName: 'Deploy Android'
  dependsOn: Build_Android
  condition: and(succeeded(), eq(${{ parameters.deploy }}, true))
  jobs:
  - job: Deploy_Firebase
    pool:
      vmImage: ubuntu-latest
    steps:
    - checkout: self
      fetchDepth: 0
    - task: UseRubyVersion@0
      inputs:
        versionSpec: '3.2.x'
        addToPath: true
      displayName: 'Setup Ruby 3.2.x'

    - script: |
        gem install bundler
        gem install fastlane
      displayName: 'Install Bundler and Fastlane'

    - script: |
        bundle install
      displayName: 'Run Bundle Install'
      
    - template: ../templates/deploy-android.yaml
      parameters:
        artifactName: 'FlutterAPKArtifact'
        appPath: 'app/equiti_platform'
        appFlavor: $(AppFlavor)
        

import 'package:equiti_analytics/src/events/analytics_event_type.dart';

enum OnboardingAnalyticsEvent {
  checkEmailStart(AnalyticsEventType.Onboarding, 'checkEmailStart'),
  checkEmailSuccess(AnalyticsEventType.Onboarding, 'checkEmailSuccess'),
  checkEmailError(AnalyticsEventType.Onboarding, 'checkEmailError'),

  loginStart(AnalyticsEventType.Onboarding, 'loginStart'),
  loginSuccess(AnalyticsEventType.Onboarding, 'loginSuccess'),
  loginOktaError(AnalyticsEventType.Onboarding, 'loginOktaError'),
  loginUaePassError(AnalyticsEventType.Onboarding, 'loginUaePassError'),

  countrySelectorStart(AnalyticsEventType.Onboarding, 'countrySelectorStart'),
  countrySelected(AnalyticsEventType.Onboarding, 'countrySelected'),
  blacklistedCountrySelected(
    AnalyticsEventType.Onboarding,
    'blacklistedCountrySelected',
  ),
  brokerRedirect(AnalyticsEventType.Onboarding, 'brokerRedirect'),
  citySelected(AnalyticsEventType.Onboarding, 'citySelected'),
  signupStart(AnalyticsEventType.Onboarding, 'signupStart'),
  signupChoice(AnalyticsEventType.Onboarding, 'signupChoice'),
  signupSuccess(AnalyticsEventType.Onboarding, 'signupSuccess'),
  signupOktaError(AnalyticsEventType.Onboarding, 'signupOktaError'),
  signupUaePassError(AnalyticsEventType.Onboarding, 'signupUaePassError'),

  entryZoneComplete(AnalyticsEventType.Onboarding, 'entryZoneComplete'),
  phoneSubmit(AnalyticsEventType.Onboarding, 'phoneSubmit'),
  phoneVerificationStart(
    AnalyticsEventType.Onboarding,
    'phoneVerificationStart',
  ),
  phoneVerificationComplete(
    AnalyticsEventType.Onboarding,
    'phoneVerificationComplete',
  ),
  phoneVerificationSkipped(
    AnalyticsEventType.Onboarding,
    'phoneVerificationSkipped',
  ),
  // New detailed error events for phone verification flows
  phoneVerificationError(
    AnalyticsEventType.Onboarding,
    'phoneVerificationError',
  ),
  sendOtpError(AnalyticsEventType.Onboarding, 'sendOtpError'),

  // New error events for data fetching and input validation
  getCountriesError(AnalyticsEventType.Onboarding, 'getCountriesError'),
  addPhoneError(AnalyticsEventType.Onboarding, 'addPhoneError'),

  formStart(AnalyticsEventType.Onboarding, 'formStart'),
  formComplete(AnalyticsEventType.Onboarding, 'formComplete'),
  formFetchError(AnalyticsEventType.Onboarding, 'formFetchError'),
  formSubmitError(AnalyticsEventType.Onboarding, 'formSubmitError'),
  sectionSubmitted(AnalyticsEventType.Onboarding, 'sectionSubmitted'),

  kycStart(AnalyticsEventType.Onboarding, 'kycStart'),
  kycSuccess(AnalyticsEventType.Onboarding, 'kycSuccess'),
  kycRejected(AnalyticsEventType.Onboarding, 'kycRejected'),
  kycPending(AnalyticsEventType.Onboarding, 'kycPending'),
  kycTemporarilyDeclined(
    AnalyticsEventType.Onboarding,
    'kycTemporarilyDeclined',
  ),
  kycStartError(AnalyticsEventType.Onboarding, 'kycStartError'),

  firstAccountStart(AnalyticsEventType.Onboarding, 'firstAccountStart'),
  firstAccountComplete(AnalyticsEventType.Onboarding, 'firstAccountComplete'),
  firstAccountError(AnalyticsEventType.Onboarding, 'firstAccountError'),

  progressTrackerError(AnalyticsEventType.Onboarding, 'progressTrackerError'),
  progressTrackerNext(AnalyticsEventType.Onboarding, 'progressTrackerNext');

  const OnboardingAnalyticsEvent(this.eventType, this.eventName);

  final AnalyticsEventType eventType;
  final String eventName;
}

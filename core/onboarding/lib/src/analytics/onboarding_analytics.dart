import 'package:equiti_analytics/equiti_analytics.dart';

class OnboardingAnalytics {
  const OnboardingAnalytics(this._analyticsService);
  final AnalyticsService _analyticsService;

  Future<bool> setGlobalAttributes(
    Map<AnalyticsGlobalAttributes, dynamic> attributes,
  ) async {
    return await _analyticsService.setGlobalAttributes(attributes);
  }

  Future<bool> removeGlobalAttributes(
    List<AnalyticsGlobalAttributes> keys,
  ) async {
    return await _analyticsService.removeGlobalAttributes(keys);
  }

  Future<bool> countrySelectorStart() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.countrySelectorStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.countrySelectorStart.eventName,
    );
  }

  Future<bool> countrySelected({
    required String selectedCountry,
    required String brokerId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.countrySelected.eventType.name,
      eventName: OnboardingAnalyticsEvent.countrySelected.eventName,
      metadata: {'selectedCountry': selectedCountry, 'brokerId': brokerId},
    );
  }

  Future<bool> blacklistedCountrySelected({
    required String selectedCountry,
    required String brokerId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          OnboardingAnalyticsEvent.blacklistedCountrySelected.eventType.name,
      eventName: OnboardingAnalyticsEvent.blacklistedCountrySelected.eventName,
      metadata: {'selectedCountry': selectedCountry, 'brokerId': brokerId},
    );
  }

  Future<bool> brokerRedirect({
    required String selectedCountry,
    required String redirectUrl,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.brokerRedirect.eventType.name,
      eventName: OnboardingAnalyticsEvent.brokerRedirect.eventName,
      metadata: {
        'selectedCountry': selectedCountry,
        'redirectUrl': redirectUrl,
      },
    );
  }

  Future<bool> citySelected({required String selectedCity}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.citySelected.eventType.name,
      eventName: OnboardingAnalyticsEvent.citySelected.eventName,
      metadata: {'selectedCity': selectedCity},
    );
  }

  Future<bool> signupStart() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.signupStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.signupStart.eventName,
    );
  }

  Future<bool> signupChoice({required String signupChannel}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.signupChoice.eventType.name,
      eventName: OnboardingAnalyticsEvent.signupChoice.eventName,
      metadata: {'signupChannel': signupChannel},
    );
  }

  Future<bool> signupSuccess({required String signupChannel}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.signupSuccess.eventType.name,
      eventName: OnboardingAnalyticsEvent.signupSuccess.eventName,
      metadata: {'signupChannel': signupChannel},
    );
  }

  Future<bool> signupOktaError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.signupOktaError.eventType.name,
      eventName: OnboardingAnalyticsEvent.signupOktaError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> signupUaePassError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.signupUaePassError.eventType.name,
      eventName: OnboardingAnalyticsEvent.signupUaePassError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> entryZoneComplete() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.entryZoneComplete.eventType.name,
      eventName: OnboardingAnalyticsEvent.entryZoneComplete.eventName,
    );
  }

  Future<bool> phoneSubmit() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.phoneSubmit.eventType.name,
      eventName: OnboardingAnalyticsEvent.phoneSubmit.eventName,
    );
  }

  Future<bool> phoneVerificationStart({
    required String verificationMethod,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.phoneVerificationStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.phoneVerificationStart.eventName,
      metadata: {'verificationMethod': verificationMethod},
    );
  }

  Future<bool> phoneVerificationComplete({
    required String verificationMethod,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          OnboardingAnalyticsEvent.phoneVerificationComplete.eventType.name,
      eventName: OnboardingAnalyticsEvent.phoneVerificationComplete.eventName,
      metadata: {'verificationMethod': verificationMethod},
    );
  }

  Future<bool> phoneVerificationSkipped() async {
    return await _analyticsService.sendEvent(
      eventType:
          OnboardingAnalyticsEvent.phoneVerificationSkipped.eventType.name,
      eventName: OnboardingAnalyticsEvent.phoneVerificationSkipped.eventName,
    );
  }

  Future<bool> formStart({
    required int formId,
    required String formName,
    required int formVersion,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.formStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.formStart.eventName,
      metadata: {
        'formId': formId,
        'formName': formName,
        'formVersion': formVersion,
      },
    );
  }

  Future<bool> formComplete({
    required int formId,
    required String formName,
    required int formVersion,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.formComplete.eventType.name,
      eventName: OnboardingAnalyticsEvent.formComplete.eventName,
      metadata: {
        'formId': formId,
        'formName': formName,
        'formVersion': formVersion,
      },
    );
  }

  Future<bool> formFetchError({
    required int errorCode,
    required String errorDescription,
    String? userRegistrationId,
    int? formId,
    String? formName,
    int? formVersion,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.formFetchError.eventType.name,
      eventName: OnboardingAnalyticsEvent.formFetchError.eventName,
      metadata: {
        'errorCode': errorCode,
        'errorDescription': errorDescription,
        if (userRegistrationId != null)
          'userRegistrationId': userRegistrationId,
        if (formId != null) 'formId': formId,
        if (formName != null) 'formName': formName,
        if (formVersion != null) 'formVersion': formVersion,
      },
    );
  }

  Future<bool> formSubmitError({
    required int errorCode,
    required String errorDescription,
    String? userRegistrationId,
    int? formId,
    String? formName,
    int? formVersion,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.formSubmitError.eventType.name,
      eventName: OnboardingAnalyticsEvent.formSubmitError.eventName,
      metadata: {
        'errorCode': errorCode,
        'errorDescription': errorDescription,
        if (userRegistrationId != null)
          'userRegistrationId': userRegistrationId,
        if (formId != null) 'formId': formId,
        if (formName != null) 'formName': formName,
        if (formVersion != null) 'formVersion': formVersion,
      },
    );
  }

  Future<bool> sectionSubmitted({
    required int sectionId,
    required String sectionName,
    required int formId,
    required String formaName,
    required int formVersion,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.sectionSubmitted.eventType.name,
      eventName: OnboardingAnalyticsEvent.sectionSubmitted.eventName,
      metadata: {
        'sectionId': sectionId,
        'sectionName': sectionName,
        'formId': formId,
        'formName': formaName,
        'formVersion': formVersion,
      },
    );
  }

  Future<bool> kycStart() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycStart.eventName,
    );
  }

  Future<bool> kycSuccess() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycSuccess.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycSuccess.eventName,
    );
  }

  Future<bool> kycRejected() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycRejected.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycRejected.eventName,
    );
  }

  Future<bool> kycPending() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycPending.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycPending.eventName,
    );
  }

  Future<bool> kycTemporarilyDeclined() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycTemporarilyDeclined.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycTemporarilyDeclined.eventName,
    );
  }

  Future<bool> kycStartError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.kycStartError.eventType.name,
      eventName: OnboardingAnalyticsEvent.kycStartError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> firstAccountStart() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.firstAccountStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.firstAccountStart.eventName,
    );
  }

  Future<bool> firstAccountComplete({
    required String platform,
    required String accountType,
    required int accountLeverage,
    required bool swapSetting,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.firstAccountComplete.eventType.name,
      eventName: OnboardingAnalyticsEvent.firstAccountComplete.eventName,
      metadata: {
        'platform': platform,
        'accountType': accountType,
        'accountLeverage': accountLeverage,
        'swapSetting': swapSetting ? 'swap-free' : 'swap-enabled',
      },
    );
  }

  Future<bool> firstAccountError({
    required String platform,
    required String accountType,
    required String accountLeverage,
    required bool swapSetting,
    int? code,
    String? message,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.firstAccountError.eventType.name,
      eventName: OnboardingAnalyticsEvent.firstAccountError.eventName,
      metadata: {
        'platform': platform,
        'accountType': accountType,
        'accountLeverage': accountLeverage,
        'swapSetting': swapSetting ? 'swap-free' : 'swap-enabled',
        'errorCode': code ?? 0,
        'errorMessage': message ?? 'NA',
      },
    );
  }

  Future<bool> phoneVerificationError({
    required String verificationMethod,
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.phoneVerificationError.eventType.name,
      eventName: OnboardingAnalyticsEvent.phoneVerificationError.eventName,
      metadata: {
        'verificationMethod': verificationMethod,
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> sendOtpError({
    required String channel,
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.sendOtpError.eventType.name,
      eventName: OnboardingAnalyticsEvent.sendOtpError.eventName,
      metadata: {
        'channel': channel,
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> getCountriesError({int? code, String? message}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.getCountriesError.eventType.name,
      eventName: OnboardingAnalyticsEvent.getCountriesError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
      },
    );
  }

  Future<bool> addPhoneError({
    required String countryCode,
    required String regionCode,
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.addPhoneError.eventType.name,
      eventName: OnboardingAnalyticsEvent.addPhoneError.eventName,
      metadata: {
        'countryCode': countryCode,
        'regionCode': regionCode,
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> progressTrackerError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.progressTrackerError.eventType.name,
      eventName: OnboardingAnalyticsEvent.progressTrackerError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> progressTrackerNext({required String currentStep}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.progressTrackerNext.eventType.name,
      eventName: OnboardingAnalyticsEvent.progressTrackerNext.eventName,
      metadata: {'currentStep': currentStep},
    );
  }
}

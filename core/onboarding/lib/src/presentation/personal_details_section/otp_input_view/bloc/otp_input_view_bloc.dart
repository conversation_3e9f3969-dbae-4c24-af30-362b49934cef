import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:onboarding/src/analytics/onboarding_analytics.dart';
import 'package:onboarding/src/data/send_otp_request_model/send_otp_request_model.dart';
import 'package:onboarding/src/data/send_otp_response_model/send_otp_response_model.dart';
import 'package:onboarding/src/data/verify_otp_model/verify_otp_model.dart';
import 'package:onboarding/src/data/verify_otp_request_model/verify_otp_request_model.dart';
import 'package:onboarding/src/domain/exceptions/send_otp_exception/send_otp_exception.dart';
import 'package:onboarding/src/domain/exceptions/verify_otp_exception/verify_otp_exception.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/domain/usecase/send_otp_usecase.dart';
import 'package:onboarding/src/domain/usecase/verify_otp_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/personal_details_section/utils/personal_details_utils.dart';
part 'otp_input_view_bloc.freezed.dart';
part 'otp_input_view_event.dart';
part 'otp_input_view_state.dart';

@injectable
class OtpInputViewBloc extends Bloc<OtpInputViewEvent, OtpInputViewState> {
  OtpInputViewBloc(
    this._navigation,
    this._verifyOtpUseCase,
    this._sendOtpUseCase,
    this._onboardingAnalytics,
  ) : super(const OtpInputViewState()) {
    on<Initial>(_initial);
    on<OtpChanged>(_onOtpChanged);
    on<ConfirmButtonPressed>(_onConfirmButtonPressed);
    on<OnEditPhoneNumberPressed>(_onOnEditPhoneNumberPressed);
    on<OnResendCodePressed>(_onOnResendCodePressed);
    on<InitArguments>(_onInitArguments);
    on<OnSkipOtpPressed>(_onSkipOtpPressed);

    // Dispatch Initial event immediately
    add(const Initial());
  }

  final OnboardingNavigation _navigation;
  final VerifyOtpUseCase _verifyOtpUseCase;
  final SendOtpUseCase _sendOtpUseCase;
  final OnboardingAnalytics _onboardingAnalytics;
  // todo (sambhav) : Add and use wrong otp count
  // int _wrongOtpCount = 0;

  Future<void> _initial(Initial event, Emitter<OtpInputViewState> emit) async {
    emit(state.copyWith(isResendButtonEnabled: false, remainingTime: 20));
    await _startTimer(emit);
  }

  Future<void> _startTimer(Emitter<OtpInputViewState> emit) async {
    await emit.forEach<int>(
      Stream.periodic(
        const Duration(seconds: 1),
        (count) => state.remainingTime - 1,
      ).takeWhile((remainingTime) => remainingTime >= 0),
      onData: (remainingTime) {
        if (remainingTime == 0) {
          return state.copyWith(isResendButtonEnabled: true, remainingTime: 0);
        }
        return state.copyWith(remainingTime: remainingTime);
      },
    );
  }

  void _onOtpChanged(OtpChanged event, Emitter<OtpInputViewState> emit) {
    emit(state.copyWith(otp: event.otp, errorMessage: ''));
    if (event.otp.length == 4) {
      emit(state.copyWith(isConfirmButtonEnabled: true));
    } else {
      emit(state.copyWith(isConfirmButtonEnabled: false));
    }
  }

  Future<void> _onConfirmButtonPressed(
    ConfirmButtonPressed event,
    Emitter<OtpInputViewState> emit,
  ) async {
    emit(state.copyWith(isConfirmButtonLoading: true));
    try {
      final result =
          await _verifyOtpUseCase(
            requestModel: VerifyOtpRequestModel(
              otpReferenceId: event.otpReferenceId,
              otpCode: state.otp,
              channel: event.channel,
            ),
          ).run();

      await Future.delayed(const Duration(seconds: 2), () {
        result.fold(
          (exception) {
            if (exception is VerifyOtpException) {
              switch (exception) {
                case VerifyOtpUnknownError(:final code, :final message):
                  {
                    _onboardingAnalytics.phoneVerificationError(
                      verificationMethod: event.channel,
                      code: code,
                      message: message,
                      errorType: 'unknown',
                    );
                    emit(
                      state.copyWith(
                        errorMessage: exception.message,
                        isConfirmButtonLoading: false,
                        processState: OtpInputProcessState.error(),
                      ),
                    );
                  }
                case VerifyOtpInvalidOtpError(:final code, :final message):
                  _onboardingAnalytics.phoneVerificationError(
                    verificationMethod: event.channel,
                    code: code,
                    message: message,
                    errorType: 'invalidOtp',
                  );
                  emit(
                    state.copyWith(
                      errorMessage: message,
                      isConfirmButtonEnabled: false,
                      processState: OtpInputProcessState.troubleWithOtp(),
                    ),
                  );
                  _resetOtpProcessState(emit);
              }
            } else {
              emit(
                state.copyWith(
                  isConfirmButtonLoading: false,
                  processState: OtpInputProcessState.error(),
                ),
              );
            }
          },
          (r) {
            switch (r) {
              case VerifyOtpModelSuccess():
                {
                  _onboardingAnalytics.phoneVerificationComplete(
                    verificationMethod: event.channel,
                  );
                  emit(state.copyWith(isConfirmButtonLoading: false));
                  final originRoute = state.args?.origin;
                  if (originRoute != null) {
                    _navigation.popUnitRoute(popUntil: originRoute);
                  } else {
                    _navigation.navigateToPhoneNumberVerified(replace: true);
                  }
                }
              case VerifyOtpModelInternalServerError():
                {
                  _onboardingAnalytics.phoneVerificationError(
                    verificationMethod: event.channel,
                    errorType: 'internalServerError',
                  );
                  emit(
                    state.copyWith(
                      isConfirmButtonLoading: false,
                      processState: OtpInputProcessState.error(),
                    ),
                  );
                  _resetOtpProcessState(emit);
                }
            }
          },
        );
      });
    } on Exception catch (e) {
      log('error while verifying otp: $e');
    } finally {
      if (!isClosed) {
        emit(state.copyWith(isConfirmButtonLoading: false));
      }
    }
  }

  void _onOnEditPhoneNumberPressed(
    OnEditPhoneNumberPressed event,
    Emitter<OtpInputViewState> emit,
  ) {
    _navigation.removeUntilMobileNumberInput();
  }

  Future<void> _onOnResendCodePressed(
    OnResendCodePressed event,
    Emitter<OtpInputViewState> emit,
  ) async {
    if (state.isResendButtonEnabled) {
      if (!isClosed) {
        emit(state.copyWith(isResendButtonEnabled: false, remainingTime: 20));
      }
      try {
        await _sendOtpUseCase(
          requestModel: SendOtpRequestModel(
            action: OtpAction.verify.name,
            channel: event.args.channel,
          ),
        ).run().then((value) {
          value.fold(
            (exception) {
              // Handle SendOtpException with switch case
              if (exception is SendOtpException) {
                switch (exception) {
                  case SendOtpUnknownError(:final code, :final message):
                    _onboardingAnalytics.sendOtpError(
                      channel: event.args.channel,
                      code: code,
                      message: message,
                      errorType: 'unknown',
                    );
                    emit(state.copyWith(errorMessage: message));
                    break;
                  case SendOtpMaxRequestError(:final code, :final message):
                    _onboardingAnalytics.sendOtpError(
                      channel: event.args.channel,
                      code: code,
                      message: message,
                      errorType: 'maxRequest',
                    );
                    emit(state.copyWith(errorMessage: message));
                    break;
                }
              } else {
                // Handle other exceptions
                //todo: need localization
                _onboardingAnalytics.sendOtpError(
                  channel: event.args.channel,
                  errorType: 'unknown',
                  message: 'Failed to send OTP. Please try again.',
                );
                emit(
                  state.copyWith(
                    errorMessage: 'Failed to send OTP. Please try again.',
                  ),
                );
              }
            },
            (r) {
              emit(state.copyWith(sendOtpModel: r.data));
            },
          );
        });
      } on Exception catch (e) {
        log('error in send otp: $e');
      }

      await _startTimer(emit);
    }
  }

  void _resetOtpProcessState(Emitter<OtpInputViewState> emit) {
    emit(state.copyWith(processState: OtpInputProcessState.initial()));
  }

  FutureOr<void> _onInitArguments(
    InitArguments event,
    Emitter<OtpInputViewState> emit,
  ) {
    emit(state.copyWith(args: event.args, sendOtpModel: event.sendOtpModel));
  }

  void _onSkipOtpPressed(
    OnSkipOtpPressed event,
    Emitter<OtpInputViewState> emit,
  ) {
    _sendOtpUseCase(
      requestModel: SendOtpRequestModel(
        action: OtpAction.skip.name,
        channel: OtpChannel.sms.name,
      ),
    ).run();
    _navigation.setAndNavigateToMorphFormBuilder();
  }
}

import 'dart:async';

import 'package:api_client/api_client.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/login.dart';
import 'package:login/src/domain/error_code.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/analytics/onboarding_analytics.dart';
import 'package:onboarding/src/domain/exceptions/sign_up_with_okta_exception/sign_up_with_okta_exception.dart';
import 'package:onboarding/src/domain/usecase/signup_with_okta_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:prelude/prelude.dart';

part 'signup_options_bloc.freezed.dart';
part 'signup_options_event.dart';
part 'signup_options_state.dart';

class SignupOptionsBloc extends Bloc<SignupOptionsEvent, SignupOptionsState> {
  final OnboardingNavigation _onboardingNavigation;
  final SignupWithOktaUsecase _signupUseCase;
  final SignupWithUaePassUsecase _signupWithUaePassUsecase;
  final LoggerBase _logger;
  final OnboardingAnalytics _onboardingAnalytics;

  SignupOptionsBloc(
    this._onboardingNavigation,
    this._signupUseCase,
    this._logger,
    this._signupWithUaePassUsecase,
    this._onboardingAnalytics,
  ) : super(SignupOptionsState()) {
    on<_NavigateToLogin>(_navigateToLogin);
    on<_StartSignup>(_startSignup);
    on<_SignUpWithUaePass>(_signUpWithUaePass);
    on<_BottomSheetClosed>(_bottomSheetClosed);
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<SignupOptionsState> emit,
  ) {
    _onboardingNavigation.goToLoginOptions(replace: true);
  }

  FutureOr<void> _startSignup(
    _StartSignup event,
    Emitter<SignupOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.loading()));
    }
    final result =
        await _signupUseCase(
          country: event.country,
          countryCode: event.countryCode,
          brokerId: event.brokerId,
          city: event.city,
        ).run();
    result.fold(
      (exception) {
        //todo: handle error properly
        if (exception is SignUpWithOktaException) {
          switch (exception) {
            case SignUpWithOktaUnknownError(:final code, :final message):
              _onboardingAnalytics.signupOktaError(
                code: code,
                message: message,
              );
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.initial(),
                  ),
                );
              }
          }
        } else {
          _onboardingAnalytics.signupOktaError(
            errorType: exception.runtimeType.toString(),
          );
          if (!isClosed) {
            emit(
              state.copyWith(processState: SignupOptionsProcessState.initial()),
            );
          }
        }
      },
      (authResult) {
        _logger.logInfo('Success! Access token: ${authResult.toString()}');
        if (!isClosed) {
          emit(
            state.copyWith(processState: SignupOptionsProcessState.initial()),
          );
        }
        final email = authResult.userData?.email;
        if (email != null) {
          // ignore: avoid-missing-enum-constant-in-map
          _onboardingAnalytics.setGlobalAttributes({
            AnalyticsGlobalAttributes.hashEmail: email.hash(),
            AnalyticsGlobalAttributes.email: email,
          });
        }

        _onboardingAnalytics.signupSuccess(signupChannel: 'okta');
        _onboardingNavigation.setAndNavigateToMorphFormBuilder();
      },
    );
  }

  FutureOr<void> _signUpWithUaePass(
    _SignUpWithUaePass event,
    Emitter<SignupOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.loading()));
    }
    final result =
        await _signupWithUaePassUsecase(
          country: event.country,
          countryCode: event.countryCode,
          brokerId: event.brokerId,
          city: event.city,
        ).run();
    result.fold(
      (exception) {
        // Handle UAE Pass exceptions
        if (exception is UaePassException) {
          _onboardingAnalytics.signupUaePassError(
            errorType: exception.code.name,
            message: exception.message,
          );
          switch (exception.code) {
            case UaePassExceptionCode.cancelled_by_user:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.userCancelled(),
                  ),
                );
              }
              break;
            case UaePassExceptionCode.unknown:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
              break;
          }
          return;
        }
        // Handle token exchange api exceptions
        if (exception is ClientException) {
          _onboardingAnalytics.signupUaePassError(
            code: exception.mobileBffBaseError?.errorCode,
            message: exception.message,
            errorType: 'ClientException',
          );
          final errorCode = exception.mobileBffBaseError?.errorCode;
          switch (errorCode) {
            case ErrorCode.USER_SIGNED_UP_WITH_DIFFERENT_CHANNEL:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState:
                        SignupOptionsProcessState.userSignedUpWithDifferentChannel(),
                  ),
                );
              }
              break;
            default:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
          }
          return;
        }
        // Handle other exceptions
        _onboardingAnalytics.signupUaePassError(
          errorType: exception.runtimeType.toString(),
          message: exception.toString(),
        );
        if (!isClosed) {
          emit(
            state.copyWith(
              processState: SignupOptionsProcessState.error(
                exception.toString(),
              ),
            ),
          );
        }
      },
      (authResult) {
        _onboardingAnalytics.signupSuccess(signupChannel: 'uaepass');
        _onboardingNavigation.setAndNavigateToMorphFormBuilder();
      },
    );
  }

  FutureOr<void> _bottomSheetClosed(
    _BottomSheetClosed event,
    Emitter<SignupOptionsState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.initial()));
    }
  }
}

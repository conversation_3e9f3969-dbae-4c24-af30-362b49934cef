import 'package:api_client/api_client.dart';
import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_secure_storage/equiti_secure_storage.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:login/login.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:preferences/preferences.dart';
import 'package:socket_client/socket_client.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:user_account/user_account.dart';

import '../mocks/auth_service_mock.dart';
import '../mocks/common_flags_mock.dart';
import '../mocks/equiti_preferences_mock.dart';
import '../mocks/locale_manager_mock.dart';
import '../mocks/onboarding_flags_mock.dart';
import '../mocks/onboarding_navigation_mock.dart';
import '../mocks/secure_storage_mock.dart';
import '../mocks/theme_manager_mock.dart';
import '../mocks/token_manager_mock.dart';

Future<void> setupDi() async {
  final gh = GetItHelper(diContainer);
  gh.lazySingleton(() => MockApiInterceptor());
  await MonitoringPackageModule().init(gh);

  gh.lazySingleton(
    () =>
        DioBuilder()
            .setBaseUrl('http://equiti-platform.com/')
            .addInterceptor(gh<PrettyDioLogger>())
            .addInterceptor(gh<MockApiInterceptor>())
            .withNativeAdapter()
            .withReporter(),
  );
  gh.lazySingleton<ApiClientBase>(
    () => DioApiClient(gh<DioBuilder>().build()),
    instanceName: 'mobileBffApiClient',
  );
  gh.lazySingleton(() => MockSocketInterceptor(gh()));
  gh.lazySingleton(
    () => SocketClient(
      gh(),
      options: SocketConnectionOptions(
        baseUrl: 'https://equiti-backend-demo-dev.equiti.me.uk/hubs/',
        unsubscribeDebounceDuration:
            Duration.zero, // Disable debouncing in tests
        isTestMode: true, // Disable timers in test mode
      ),
      interceptors: [gh<MockSocketInterceptor>()],
    ),
  );
  gh.singleton<AuthService>(() => AuthServiceMock());

  gh.lazySingleton<EquitiPreferences>(() => EquitiPreferencesMock());
  gh.lazySingleton<LocaleManager>(() => LocaleManagerMock());
  gh.lazySingleton<OnboardingNavigation>(() => OnboardingNavigationMock());
  gh.lazySingleton<ThemeManager>(() => ThemeManagerMock());
  gh.lazySingleton(() => GlobalKey<NavigatorState>());
  gh.lazySingleton<TokenManager>(() => TokenManagerMock());
  gh.lazySingleton<CommonFlags>(() => CommonFlagsMock());
  gh.lazySingleton<OnboardingFlags>(() => OnboardingFlagsMock());
  gh.lazySingleton<SecureStorage>(() => SecureStorageMock());

  await EquitiAnalyticsPackageModule().init(gh);
  await OnboardingPackageModule().init(gh);
  await UserAccountPackageModule().init(gh);
  await BrokerSettingsPackageModule().init(gh);
  await DomainPackageModule().init(gh);
  await DuploPackageModule().init(gh);
  await LoginPackageModule().init(gh);
}

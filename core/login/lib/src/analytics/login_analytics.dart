import 'package:equiti_analytics/equiti_analytics.dart';

class LoginAnalytics {
  const LoginAnalytics(this._analyticsService);

  final AnalyticsService _analyticsService;

  Future<bool> setGlobalAttributes(
    Map<AnalyticsGlobalAttributes, dynamic> attributes,
  ) async {
    return await _analyticsService.setGlobalAttributes(attributes);
  }

  Future<bool> checkEmailStart({required String email}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.checkEmailStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.checkEmailStart.eventName,
      metadata: {'email': email},
    );
  }

  Future<bool> checkEmailSuccess({
    required String email,
    required String migrationStatus,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.checkEmailSuccess.eventType.name,
      eventName: OnboardingAnalyticsEvent.checkEmailSuccess.eventName,
      metadata: {'email': email, 'migrationStatus': migrationStatus},
    );
  }

  Future<bool> checkEmailError({
    required String email,
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.checkEmailError.eventType.name,
      eventName: OnboardingAnalyticsEvent.checkEmailError.eventName,
      metadata: {
        'email': email,
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> loginStart() async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.loginStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.loginStart.eventName,
    );
  }

  Future<bool> loginSuccess({required String channel}) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.loginSuccess.eventType.name,
      eventName: OnboardingAnalyticsEvent.loginSuccess.eventName,
      metadata: {'channel': channel},
    );
  }

  Future<bool> loginOktaError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.loginOktaError.eventType.name,
      eventName: OnboardingAnalyticsEvent.loginOktaError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }

  Future<bool> loginUaePassError({
    int? code,
    String? message,
    String? errorType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: OnboardingAnalyticsEvent.loginUaePassError.eventType.name,
      eventName: OnboardingAnalyticsEvent.loginUaePassError.eventName,
      metadata: {
        if (code != null) 'errorCode': code,
        if (message != null) 'errorMessage': message,
        if (errorType != null) 'errorType': errorType,
      },
    );
  }
}

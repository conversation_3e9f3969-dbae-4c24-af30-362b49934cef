import 'package:domain/domain.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';

abstract class EquitiTraderNavigation {
  void navigateToSymbols();

  void navigateToProductDetail({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
    TradeType? tradeDirection,
  });

  void navigateToLogin({String? email});
  void navigateToSignUpOptions();
  void navigateToLoginOptions();
  void navigateToTradingPrefrences();
  void navigateToPortfolio({TradeConfirmationResult? result});
  void navigateToHub();
  void navigateToSwitchAccounts();
  void navigateToDepositOptions({required DepositFlowConfig depositFlowConfig});
  void navigateToWithdrawOptions(String popUntilRoute);
  void goToTransferFundsScreen(String originRoute);
  void navigateToPerformance();
  void navigateToHubSettings();
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
    required void Function() thenCallback,
  });
  void navigateToCreateWallet();
}

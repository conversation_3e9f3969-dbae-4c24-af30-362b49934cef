// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symbol_detail_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymbolDetailArgs {

 SymbolDetailViewModel get symbolDetail; String get accountNumber; TradeType? get tradeDirection;
/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SymbolDetailArgsCopyWith<SymbolDetailArgs> get copyWith => _$SymbolDetailArgsCopyWithImpl<SymbolDetailArgs>(this as SymbolDetailArgs, _$identity);

  /// Serializes this SymbolDetailArgs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolDetailArgs&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.tradeDirection, tradeDirection) || other.tradeDirection == tradeDirection));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,symbolDetail,accountNumber,tradeDirection);

@override
String toString() {
  return 'SymbolDetailArgs(symbolDetail: $symbolDetail, accountNumber: $accountNumber, tradeDirection: $tradeDirection)';
}


}

/// @nodoc
abstract mixin class $SymbolDetailArgsCopyWith<$Res>  {
  factory $SymbolDetailArgsCopyWith(SymbolDetailArgs value, $Res Function(SymbolDetailArgs) _then) = _$SymbolDetailArgsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel symbolDetail, String accountNumber, TradeType? tradeDirection
});


$SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class _$SymbolDetailArgsCopyWithImpl<$Res>
    implements $SymbolDetailArgsCopyWith<$Res> {
  _$SymbolDetailArgsCopyWithImpl(this._self, this._then);

  final SymbolDetailArgs _self;
  final $Res Function(SymbolDetailArgs) _then;

/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolDetail = null,Object? accountNumber = null,Object? tradeDirection = freezed,}) {
  return _then(_self.copyWith(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,tradeDirection: freezed == tradeDirection ? _self.tradeDirection : tradeDirection // ignore: cast_nullable_to_non_nullable
as TradeType?,
  ));
}
/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _SymbolDetailArgs implements SymbolDetailArgs {
  const _SymbolDetailArgs({required this.symbolDetail, required this.accountNumber, this.tradeDirection});
  factory _SymbolDetailArgs.fromJson(Map<String, dynamic> json) => _$SymbolDetailArgsFromJson(json);

@override final  SymbolDetailViewModel symbolDetail;
@override final  String accountNumber;
@override final  TradeType? tradeDirection;

/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolDetailArgsCopyWith<_SymbolDetailArgs> get copyWith => __$SymbolDetailArgsCopyWithImpl<_SymbolDetailArgs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SymbolDetailArgsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SymbolDetailArgs&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.tradeDirection, tradeDirection) || other.tradeDirection == tradeDirection));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,symbolDetail,accountNumber,tradeDirection);

@override
String toString() {
  return 'SymbolDetailArgs(symbolDetail: $symbolDetail, accountNumber: $accountNumber, tradeDirection: $tradeDirection)';
}


}

/// @nodoc
abstract mixin class _$SymbolDetailArgsCopyWith<$Res> implements $SymbolDetailArgsCopyWith<$Res> {
  factory _$SymbolDetailArgsCopyWith(_SymbolDetailArgs value, $Res Function(_SymbolDetailArgs) _then) = __$SymbolDetailArgsCopyWithImpl;
@override @useResult
$Res call({
 SymbolDetailViewModel symbolDetail, String accountNumber, TradeType? tradeDirection
});


@override $SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class __$SymbolDetailArgsCopyWithImpl<$Res>
    implements _$SymbolDetailArgsCopyWith<$Res> {
  __$SymbolDetailArgsCopyWithImpl(this._self, this._then);

  final _SymbolDetailArgs _self;
  final $Res Function(_SymbolDetailArgs) _then;

/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolDetail = null,Object? accountNumber = null,Object? tradeDirection = freezed,}) {
  return _then(_SymbolDetailArgs(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,tradeDirection: freezed == tradeDirection ? _self.tradeDirection : tradeDirection // ignore: cast_nullable_to_non_nullable
as TradeType?,
  ));
}

/// Create a copy of SymbolDetailArgs
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

// dart format on

import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'symbol_detail_args.freezed.dart';
part 'symbol_detail_args.g.dart';

@freezed
abstract class SymbolDetailArgs with _$SymbolDetailArgs {
  const factory SymbolDetailArgs({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
    TradeType? tradeDirection,
  }) = _SymbolDetailArgs;

  factory SymbolDetailArgs.fromJson(Map<String, dynamic> json) =>
      _$SymbolDetailArgsFromJson(json);
}

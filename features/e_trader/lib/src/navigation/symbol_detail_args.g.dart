// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'symbol_detail_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SymbolDetailArgs _$SymbolDetailArgsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_SymbolDetailArgs', json, ($checkedConvert) {
      final val = _SymbolDetailArgs(
        symbolDetail: $checkedConvert(
          'symbolDetail',
          (v) => SymbolDetailViewModel.fromJson(v as Map<String, dynamic>),
        ),
        accountNumber: $checkedConvert('accountNumber', (v) => v as String),
        tradeDirection: $checkedConvert(
          'tradeDirection',
          (v) => $enumDecodeNullable(_$TradeTypeEnumMap, v),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SymbolDetailArgsToJson(_SymbolDetailArgs instance) =>
    <String, dynamic>{
      'symbolDetail': instance.symbolDetail.toJson(),
      'accountNumber': instance.accountNumber,
      if (_$TradeTypeEnumMap[instance.tradeDirection] case final value?)
        'tradeDirection': value,
    };

const _$TradeTypeEnumMap = {TradeType.buy: 'buy', TradeType.sell: 'sell'};

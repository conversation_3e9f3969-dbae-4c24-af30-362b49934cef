import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/navigation/symbol_detail_args.dart';
import 'package:e_trader/src/presentation/product_details/product_details_widget.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';

class SymbolDetailPage extends EquitiPage {
  const SymbolDetailPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    final arguments = routeData.arguments as SymbolDetailArgs;
    final symbolDetail = arguments.symbolDetail;
    final account = arguments.accountNumber;
    final tradeDirection = arguments.tradeDirection;

    return ProductDetailsWidget(
      symbolDetail: symbolDetail,
      accountNumber: account,
      tradeDirection: tradeDirection,
    );
  }

  @override
  String get label => EquitiTraderRouteSchema.symbolsDetailRoute.label;

  @override
  String get url => EquitiTraderRouteSchema.symbolsDetailRoute.url;
}

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'position_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PositionModel {

@JsonKey(name: 'productCategoryId') String get productCategoryId;@JsonKey(name: 'productCategoryId') set productCategoryId(String value);@JsonKey(name: 'positionId') String get positionId;@JsonKey(name: 'positionId') set positionId(String value);@JsonKey(name: 'profit') double? get profit;@JsonKey(name: 'profit') set profit(double? value);@JsonKey(name: 'symbol') String get platformName;@JsonKey(name: 'symbol') set platformName(String value);@JsonKey(name: 'productName') String get tickerName;@JsonKey(name: 'productName') set tickerName(String value);@JsonKey(name: 'positionType')@TradeTypeIntConverter() TradeType get positionType;@JsonKey(name: 'positionType')@TradeTypeIntConverter() set positionType(TradeType value);@JsonKey(name: 'openPrice') double get openPrice;@JsonKey(name: 'openPrice') set openPrice(double value);@JsonKey(name: 'currentPrice') double get currentPrice;@JsonKey(name: 'currentPrice') set currentPrice(double value);@JsonKey(name: 'percentageChange') double get percentageChange;@JsonKey(name: 'percentageChange') set percentageChange(double value);@JsonKey(name: 'stopLoss') double get stopLoss;@JsonKey(name: 'stopLoss') set stopLoss(double value);@JsonKey(name: 'takeProfit') double get takeProfit;@JsonKey(name: 'takeProfit') set takeProfit(double value);@JsonKey(name: 'swap') double get swap;@JsonKey(name: 'swap') set swap(double value);@JsonKey(name: 'volume') int get volume;@JsonKey(name: 'volume') set volume(int value);@JsonKey(name: 'marginRate') double get marginRate;@JsonKey(name: 'marginRate') set marginRate(double value);@JsonKey(name: 'tradingAccountNumber') String? get tradingAccountNumber;@JsonKey(name: 'tradingAccountNumber') set tradingAccountNumber(String? value);@JsonKey(name: 'profitRate') double get profitRate;@JsonKey(name: 'profitRate') set profitRate(double value);@JsonKey(name: 'contractSize') double get contractSize;@JsonKey(name: 'contractSize') set contractSize(double value);@JsonKey(name: 'margin') double get margin;@JsonKey(name: 'margin') set margin(double value);@JsonKey(name: 'marginAllocation') double get marginAllocation;@JsonKey(name: 'marginAllocation') set marginAllocation(double value);@JsonKey(name: 'productCategory') String get productCategory;@JsonKey(name: 'productCategory') set productCategory(String value);@JsonKey(name: 'commission') double get commission;@JsonKey(name: 'commission') set commission(double value);@JsonKey(name: 'leverage') int get leverage;@JsonKey(name: 'leverage') set leverage(int value);@JsonKey(name: 'digits') int get digits;@JsonKey(name: 'digits') set digits(int value);@JsonKey(name: 'productLogoUrl') String get productLogoUrl;@JsonKey(name: 'productLogoUrl') set productLogoUrl(String value);@JsonKey(name: 'notionalValue') double get notionalValue;@JsonKey(name: 'notionalValue') set notionalValue(double value);@JsonKey(name: 'pipValue') double get pipValue;@JsonKey(name: 'pipValue') set pipValue(double value);@JsonKey(name: 'assetType') String get assetType;@JsonKey(name: 'assetType') set assetType(String value);@JsonKey(name: 'sector') String get sector;@JsonKey(name: 'sector') set sector(String value);@JsonKey(name: 'baseCurrency') String get baseCurrency;@JsonKey(name: 'baseCurrency') set baseCurrency(String value);@JsonKey(name: 'grossProfit') double get grossProfit;@JsonKey(name: 'grossProfit') set grossProfit(double value);@JsonKey(name: 'minLot') double get minLot;@JsonKey(name: 'minLot') set minLot(double value);@JsonKey(name: 'maxLot') double get maxLot;@JsonKey(name: 'maxLot') set maxLot(double value);@JsonKey(name: 'accountUsdCurrencyPair') String? get accountUsdCurrencyPair;@JsonKey(name: 'accountUsdCurrencyPair') set accountUsdCurrencyPair(String? value);@JsonKey(name: 'isUpdated') bool? get isUpdated;@JsonKey(name: 'isUpdated') set isUpdated(bool? value);@JsonKey(name: 'marginLevel') double get marginLevel;@JsonKey(name: 'marginLevel') set marginLevel(double value);@JsonKey(name: 'messageType') int get messageType;@JsonKey(name: 'messageType') set messageType(int value);@JsonKey(name: 'notionalUsd') double? get notionalUsd;@JsonKey(name: 'notionalUsd') set notionalUsd(double? value);@JsonKey(name: 'openedAt', fromJson: _formatDate) String get openedAt;@JsonKey(name: 'openedAt', fromJson: _formatDate) set openedAt(String value);@JsonKey(name: 'profitUsd') double? get profitUsd;@JsonKey(name: 'profitUsd') set profitUsd(double? value);@JsonKey(name: 'updatedAt') String get updatedAt;@JsonKey(name: 'updatedAt') set updatedAt(String value);@JsonKey(name: 'usdRate') double? get usdRate;@JsonKey(name: 'usdRate') set usdRate(double? value);@JsonKey(name: 'multiply') int get multiply;@JsonKey(name: 'multiply') set multiply(int value);@JsonKey(name: 'priceDirection')@PriceDirectionIntConverter() PriceDirection get priceDirection;@JsonKey(name: 'priceDirection')@PriceDirectionIntConverter() set priceDirection(PriceDirection value);
/// Create a copy of PositionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PositionModelCopyWith<PositionModel> get copyWith => _$PositionModelCopyWithImpl<PositionModel>(this as PositionModel, _$identity);

  /// Serializes this PositionModel to a JSON map.
  Map<String, dynamic> toJson();




@override
String toString() {
  return 'PositionModel(productCategoryId: $productCategoryId, positionId: $positionId, profit: $profit, platformName: $platformName, tickerName: $tickerName, positionType: $positionType, openPrice: $openPrice, currentPrice: $currentPrice, percentageChange: $percentageChange, stopLoss: $stopLoss, takeProfit: $takeProfit, swap: $swap, volume: $volume, marginRate: $marginRate, tradingAccountNumber: $tradingAccountNumber, profitRate: $profitRate, contractSize: $contractSize, margin: $margin, marginAllocation: $marginAllocation, productCategory: $productCategory, commission: $commission, leverage: $leverage, digits: $digits, productLogoUrl: $productLogoUrl, notionalValue: $notionalValue, pipValue: $pipValue, assetType: $assetType, sector: $sector, baseCurrency: $baseCurrency, grossProfit: $grossProfit, minLot: $minLot, maxLot: $maxLot, accountUsdCurrencyPair: $accountUsdCurrencyPair, isUpdated: $isUpdated, marginLevel: $marginLevel, messageType: $messageType, notionalUsd: $notionalUsd, openedAt: $openedAt, profitUsd: $profitUsd, updatedAt: $updatedAt, usdRate: $usdRate, multiply: $multiply, priceDirection: $priceDirection)';
}


}

/// @nodoc
abstract mixin class $PositionModelCopyWith<$Res>  {
  factory $PositionModelCopyWith(PositionModel value, $Res Function(PositionModel) _then) = _$PositionModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'productCategoryId') String productCategoryId,@JsonKey(name: 'positionId') String positionId,@JsonKey(name: 'profit') double? profit,@JsonKey(name: 'symbol') String platformName,@JsonKey(name: 'productName') String tickerName,@JsonKey(name: 'positionType')@TradeTypeIntConverter() TradeType positionType,@JsonKey(name: 'openPrice') double openPrice,@JsonKey(name: 'currentPrice') double currentPrice,@JsonKey(name: 'percentageChange') double percentageChange,@JsonKey(name: 'stopLoss') double stopLoss,@JsonKey(name: 'takeProfit') double takeProfit,@JsonKey(name: 'swap') double swap,@JsonKey(name: 'volume') int volume,@JsonKey(name: 'marginRate') double marginRate,@JsonKey(name: 'tradingAccountNumber') String? tradingAccountNumber,@JsonKey(name: 'profitRate') double profitRate,@JsonKey(name: 'contractSize') double contractSize,@JsonKey(name: 'margin') double margin,@JsonKey(name: 'marginAllocation') double marginAllocation,@JsonKey(name: 'productCategory') String productCategory,@JsonKey(name: 'commission') double commission,@JsonKey(name: 'leverage') int leverage,@JsonKey(name: 'digits') int digits,@JsonKey(name: 'productLogoUrl') String productLogoUrl,@JsonKey(name: 'notionalValue') double notionalValue,@JsonKey(name: 'pipValue') double pipValue,@JsonKey(name: 'assetType') String assetType,@JsonKey(name: 'sector') String sector,@JsonKey(name: 'baseCurrency') String baseCurrency,@JsonKey(name: 'grossProfit') double grossProfit,@JsonKey(name: 'minLot') double minLot,@JsonKey(name: 'maxLot') double maxLot,@JsonKey(name: 'accountUsdCurrencyPair') String? accountUsdCurrencyPair,@JsonKey(name: 'isUpdated') bool? isUpdated,@JsonKey(name: 'marginLevel') double marginLevel,@JsonKey(name: 'messageType') int messageType,@JsonKey(name: 'notionalUsd') double? notionalUsd,@JsonKey(name: 'openedAt', fromJson: _formatDate) String openedAt,@JsonKey(name: 'profitUsd') double? profitUsd,@JsonKey(name: 'updatedAt') String updatedAt,@JsonKey(name: 'usdRate') double? usdRate,@JsonKey(name: 'multiply') int multiply,@JsonKey(name: 'priceDirection')@PriceDirectionIntConverter() PriceDirection priceDirection
});




}
/// @nodoc
class _$PositionModelCopyWithImpl<$Res>
    implements $PositionModelCopyWith<$Res> {
  _$PositionModelCopyWithImpl(this._self, this._then);

  final PositionModel _self;
  final $Res Function(PositionModel) _then;

/// Create a copy of PositionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productCategoryId = null,Object? positionId = null,Object? profit = freezed,Object? platformName = null,Object? tickerName = null,Object? positionType = null,Object? openPrice = null,Object? currentPrice = null,Object? percentageChange = null,Object? stopLoss = null,Object? takeProfit = null,Object? swap = null,Object? volume = null,Object? marginRate = null,Object? tradingAccountNumber = freezed,Object? profitRate = null,Object? contractSize = null,Object? margin = null,Object? marginAllocation = null,Object? productCategory = null,Object? commission = null,Object? leverage = null,Object? digits = null,Object? productLogoUrl = null,Object? notionalValue = null,Object? pipValue = null,Object? assetType = null,Object? sector = null,Object? baseCurrency = null,Object? grossProfit = null,Object? minLot = null,Object? maxLot = null,Object? accountUsdCurrencyPair = freezed,Object? isUpdated = freezed,Object? marginLevel = null,Object? messageType = null,Object? notionalUsd = freezed,Object? openedAt = null,Object? profitUsd = freezed,Object? updatedAt = null,Object? usdRate = freezed,Object? multiply = null,Object? priceDirection = null,}) {
  return _then(_self.copyWith(
productCategoryId: null == productCategoryId ? _self.productCategoryId : productCategoryId // ignore: cast_nullable_to_non_nullable
as String,positionId: null == positionId ? _self.positionId : positionId // ignore: cast_nullable_to_non_nullable
as String,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,platformName: null == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String,tickerName: null == tickerName ? _self.tickerName : tickerName // ignore: cast_nullable_to_non_nullable
as String,positionType: null == positionType ? _self.positionType : positionType // ignore: cast_nullable_to_non_nullable
as TradeType,openPrice: null == openPrice ? _self.openPrice : openPrice // ignore: cast_nullable_to_non_nullable
as double,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,percentageChange: null == percentageChange ? _self.percentageChange : percentageChange // ignore: cast_nullable_to_non_nullable
as double,stopLoss: null == stopLoss ? _self.stopLoss : stopLoss // ignore: cast_nullable_to_non_nullable
as double,takeProfit: null == takeProfit ? _self.takeProfit : takeProfit // ignore: cast_nullable_to_non_nullable
as double,swap: null == swap ? _self.swap : swap // ignore: cast_nullable_to_non_nullable
as double,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int,marginRate: null == marginRate ? _self.marginRate : marginRate // ignore: cast_nullable_to_non_nullable
as double,tradingAccountNumber: freezed == tradingAccountNumber ? _self.tradingAccountNumber : tradingAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,profitRate: null == profitRate ? _self.profitRate : profitRate // ignore: cast_nullable_to_non_nullable
as double,contractSize: null == contractSize ? _self.contractSize : contractSize // ignore: cast_nullable_to_non_nullable
as double,margin: null == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double,marginAllocation: null == marginAllocation ? _self.marginAllocation : marginAllocation // ignore: cast_nullable_to_non_nullable
as double,productCategory: null == productCategory ? _self.productCategory : productCategory // ignore: cast_nullable_to_non_nullable
as String,commission: null == commission ? _self.commission : commission // ignore: cast_nullable_to_non_nullable
as double,leverage: null == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,productLogoUrl: null == productLogoUrl ? _self.productLogoUrl : productLogoUrl // ignore: cast_nullable_to_non_nullable
as String,notionalValue: null == notionalValue ? _self.notionalValue : notionalValue // ignore: cast_nullable_to_non_nullable
as double,pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as double,assetType: null == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String,sector: null == sector ? _self.sector : sector // ignore: cast_nullable_to_non_nullable
as String,baseCurrency: null == baseCurrency ? _self.baseCurrency : baseCurrency // ignore: cast_nullable_to_non_nullable
as String,grossProfit: null == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as double,minLot: null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,maxLot: null == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double,accountUsdCurrencyPair: freezed == accountUsdCurrencyPair ? _self.accountUsdCurrencyPair : accountUsdCurrencyPair // ignore: cast_nullable_to_non_nullable
as String?,isUpdated: freezed == isUpdated ? _self.isUpdated : isUpdated // ignore: cast_nullable_to_non_nullable
as bool?,marginLevel: null == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double,messageType: null == messageType ? _self.messageType : messageType // ignore: cast_nullable_to_non_nullable
as int,notionalUsd: freezed == notionalUsd ? _self.notionalUsd : notionalUsd // ignore: cast_nullable_to_non_nullable
as double?,openedAt: null == openedAt ? _self.openedAt : openedAt // ignore: cast_nullable_to_non_nullable
as String,profitUsd: freezed == profitUsd ? _self.profitUsd : profitUsd // ignore: cast_nullable_to_non_nullable
as double?,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as String,usdRate: freezed == usdRate ? _self.usdRate : usdRate // ignore: cast_nullable_to_non_nullable
as double?,multiply: null == multiply ? _self.multiply : multiply // ignore: cast_nullable_to_non_nullable
as int,priceDirection: null == priceDirection ? _self.priceDirection : priceDirection // ignore: cast_nullable_to_non_nullable
as PriceDirection,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PositionModel extends PositionModel {
   _PositionModel({@JsonKey(name: 'productCategoryId') required this.productCategoryId, @JsonKey(name: 'positionId') required this.positionId, @JsonKey(name: 'profit') this.profit, @JsonKey(name: 'symbol') required this.platformName, @JsonKey(name: 'productName') required this.tickerName, @JsonKey(name: 'positionType')@TradeTypeIntConverter() required this.positionType, @JsonKey(name: 'openPrice') required this.openPrice, @JsonKey(name: 'currentPrice') required this.currentPrice, @JsonKey(name: 'percentageChange') required this.percentageChange, @JsonKey(name: 'stopLoss') required this.stopLoss, @JsonKey(name: 'takeProfit') required this.takeProfit, @JsonKey(name: 'swap') required this.swap, @JsonKey(name: 'volume') required this.volume, @JsonKey(name: 'marginRate') required this.marginRate, @JsonKey(name: 'tradingAccountNumber') this.tradingAccountNumber, @JsonKey(name: 'profitRate') required this.profitRate, @JsonKey(name: 'contractSize') required this.contractSize, @JsonKey(name: 'margin') required this.margin, @JsonKey(name: 'marginAllocation') required this.marginAllocation, @JsonKey(name: 'productCategory') required this.productCategory, @JsonKey(name: 'commission') required this.commission, @JsonKey(name: 'leverage') required this.leverage, @JsonKey(name: 'digits') required this.digits, @JsonKey(name: 'productLogoUrl') required this.productLogoUrl, @JsonKey(name: 'notionalValue') required this.notionalValue, @JsonKey(name: 'pipValue') required this.pipValue, @JsonKey(name: 'assetType') required this.assetType, @JsonKey(name: 'sector') required this.sector, @JsonKey(name: 'baseCurrency') required this.baseCurrency, @JsonKey(name: 'grossProfit') required this.grossProfit, @JsonKey(name: 'minLot') required this.minLot, @JsonKey(name: 'maxLot') required this.maxLot, @JsonKey(name: 'accountUsdCurrencyPair') this.accountUsdCurrencyPair, @JsonKey(name: 'isUpdated') this.isUpdated, @JsonKey(name: 'marginLevel') required this.marginLevel, @JsonKey(name: 'messageType') required this.messageType, @JsonKey(name: 'notionalUsd') this.notionalUsd, @JsonKey(name: 'openedAt', fromJson: _formatDate) required this.openedAt, @JsonKey(name: 'profitUsd') this.profitUsd, @JsonKey(name: 'updatedAt') required this.updatedAt, @JsonKey(name: 'usdRate') this.usdRate, @JsonKey(name: 'multiply') required this.multiply, @JsonKey(name: 'priceDirection')@PriceDirectionIntConverter() required this.priceDirection}): super._();
  factory _PositionModel.fromJson(Map<String, dynamic> json) => _$PositionModelFromJson(json);

@override@JsonKey(name: 'productCategoryId')  String productCategoryId;
@override@JsonKey(name: 'positionId')  String positionId;
@override@JsonKey(name: 'profit')  double? profit;
@override@JsonKey(name: 'symbol')  String platformName;
@override@JsonKey(name: 'productName')  String tickerName;
@override@JsonKey(name: 'positionType')@TradeTypeIntConverter()  TradeType positionType;
@override@JsonKey(name: 'openPrice')  double openPrice;
@override@JsonKey(name: 'currentPrice')  double currentPrice;
@override@JsonKey(name: 'percentageChange')  double percentageChange;
@override@JsonKey(name: 'stopLoss')  double stopLoss;
@override@JsonKey(name: 'takeProfit')  double takeProfit;
@override@JsonKey(name: 'swap')  double swap;
@override@JsonKey(name: 'volume')  int volume;
@override@JsonKey(name: 'marginRate')  double marginRate;
@override@JsonKey(name: 'tradingAccountNumber')  String? tradingAccountNumber;
@override@JsonKey(name: 'profitRate')  double profitRate;
@override@JsonKey(name: 'contractSize')  double contractSize;
@override@JsonKey(name: 'margin')  double margin;
@override@JsonKey(name: 'marginAllocation')  double marginAllocation;
@override@JsonKey(name: 'productCategory')  String productCategory;
@override@JsonKey(name: 'commission')  double commission;
@override@JsonKey(name: 'leverage')  int leverage;
@override@JsonKey(name: 'digits')  int digits;
@override@JsonKey(name: 'productLogoUrl')  String productLogoUrl;
@override@JsonKey(name: 'notionalValue')  double notionalValue;
@override@JsonKey(name: 'pipValue')  double pipValue;
@override@JsonKey(name: 'assetType')  String assetType;
@override@JsonKey(name: 'sector')  String sector;
@override@JsonKey(name: 'baseCurrency')  String baseCurrency;
@override@JsonKey(name: 'grossProfit')  double grossProfit;
@override@JsonKey(name: 'minLot')  double minLot;
@override@JsonKey(name: 'maxLot')  double maxLot;
@override@JsonKey(name: 'accountUsdCurrencyPair')  String? accountUsdCurrencyPair;
@override@JsonKey(name: 'isUpdated')  bool? isUpdated;
@override@JsonKey(name: 'marginLevel')  double marginLevel;
@override@JsonKey(name: 'messageType')  int messageType;
@override@JsonKey(name: 'notionalUsd')  double? notionalUsd;
@override@JsonKey(name: 'openedAt', fromJson: _formatDate)  String openedAt;
@override@JsonKey(name: 'profitUsd')  double? profitUsd;
@override@JsonKey(name: 'updatedAt')  String updatedAt;
@override@JsonKey(name: 'usdRate')  double? usdRate;
@override@JsonKey(name: 'multiply')  int multiply;
@override@JsonKey(name: 'priceDirection')@PriceDirectionIntConverter()  PriceDirection priceDirection;

/// Create a copy of PositionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PositionModelCopyWith<_PositionModel> get copyWith => __$PositionModelCopyWithImpl<_PositionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PositionModelToJson(this, );
}



@override
String toString() {
  return 'PositionModel(productCategoryId: $productCategoryId, positionId: $positionId, profit: $profit, platformName: $platformName, tickerName: $tickerName, positionType: $positionType, openPrice: $openPrice, currentPrice: $currentPrice, percentageChange: $percentageChange, stopLoss: $stopLoss, takeProfit: $takeProfit, swap: $swap, volume: $volume, marginRate: $marginRate, tradingAccountNumber: $tradingAccountNumber, profitRate: $profitRate, contractSize: $contractSize, margin: $margin, marginAllocation: $marginAllocation, productCategory: $productCategory, commission: $commission, leverage: $leverage, digits: $digits, productLogoUrl: $productLogoUrl, notionalValue: $notionalValue, pipValue: $pipValue, assetType: $assetType, sector: $sector, baseCurrency: $baseCurrency, grossProfit: $grossProfit, minLot: $minLot, maxLot: $maxLot, accountUsdCurrencyPair: $accountUsdCurrencyPair, isUpdated: $isUpdated, marginLevel: $marginLevel, messageType: $messageType, notionalUsd: $notionalUsd, openedAt: $openedAt, profitUsd: $profitUsd, updatedAt: $updatedAt, usdRate: $usdRate, multiply: $multiply, priceDirection: $priceDirection)';
}


}

/// @nodoc
abstract mixin class _$PositionModelCopyWith<$Res> implements $PositionModelCopyWith<$Res> {
  factory _$PositionModelCopyWith(_PositionModel value, $Res Function(_PositionModel) _then) = __$PositionModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'productCategoryId') String productCategoryId,@JsonKey(name: 'positionId') String positionId,@JsonKey(name: 'profit') double? profit,@JsonKey(name: 'symbol') String platformName,@JsonKey(name: 'productName') String tickerName,@JsonKey(name: 'positionType')@TradeTypeIntConverter() TradeType positionType,@JsonKey(name: 'openPrice') double openPrice,@JsonKey(name: 'currentPrice') double currentPrice,@JsonKey(name: 'percentageChange') double percentageChange,@JsonKey(name: 'stopLoss') double stopLoss,@JsonKey(name: 'takeProfit') double takeProfit,@JsonKey(name: 'swap') double swap,@JsonKey(name: 'volume') int volume,@JsonKey(name: 'marginRate') double marginRate,@JsonKey(name: 'tradingAccountNumber') String? tradingAccountNumber,@JsonKey(name: 'profitRate') double profitRate,@JsonKey(name: 'contractSize') double contractSize,@JsonKey(name: 'margin') double margin,@JsonKey(name: 'marginAllocation') double marginAllocation,@JsonKey(name: 'productCategory') String productCategory,@JsonKey(name: 'commission') double commission,@JsonKey(name: 'leverage') int leverage,@JsonKey(name: 'digits') int digits,@JsonKey(name: 'productLogoUrl') String productLogoUrl,@JsonKey(name: 'notionalValue') double notionalValue,@JsonKey(name: 'pipValue') double pipValue,@JsonKey(name: 'assetType') String assetType,@JsonKey(name: 'sector') String sector,@JsonKey(name: 'baseCurrency') String baseCurrency,@JsonKey(name: 'grossProfit') double grossProfit,@JsonKey(name: 'minLot') double minLot,@JsonKey(name: 'maxLot') double maxLot,@JsonKey(name: 'accountUsdCurrencyPair') String? accountUsdCurrencyPair,@JsonKey(name: 'isUpdated') bool? isUpdated,@JsonKey(name: 'marginLevel') double marginLevel,@JsonKey(name: 'messageType') int messageType,@JsonKey(name: 'notionalUsd') double? notionalUsd,@JsonKey(name: 'openedAt', fromJson: _formatDate) String openedAt,@JsonKey(name: 'profitUsd') double? profitUsd,@JsonKey(name: 'updatedAt') String updatedAt,@JsonKey(name: 'usdRate') double? usdRate,@JsonKey(name: 'multiply') int multiply,@JsonKey(name: 'priceDirection')@PriceDirectionIntConverter() PriceDirection priceDirection
});




}
/// @nodoc
class __$PositionModelCopyWithImpl<$Res>
    implements _$PositionModelCopyWith<$Res> {
  __$PositionModelCopyWithImpl(this._self, this._then);

  final _PositionModel _self;
  final $Res Function(_PositionModel) _then;

/// Create a copy of PositionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productCategoryId = null,Object? positionId = null,Object? profit = freezed,Object? platformName = null,Object? tickerName = null,Object? positionType = null,Object? openPrice = null,Object? currentPrice = null,Object? percentageChange = null,Object? stopLoss = null,Object? takeProfit = null,Object? swap = null,Object? volume = null,Object? marginRate = null,Object? tradingAccountNumber = freezed,Object? profitRate = null,Object? contractSize = null,Object? margin = null,Object? marginAllocation = null,Object? productCategory = null,Object? commission = null,Object? leverage = null,Object? digits = null,Object? productLogoUrl = null,Object? notionalValue = null,Object? pipValue = null,Object? assetType = null,Object? sector = null,Object? baseCurrency = null,Object? grossProfit = null,Object? minLot = null,Object? maxLot = null,Object? accountUsdCurrencyPair = freezed,Object? isUpdated = freezed,Object? marginLevel = null,Object? messageType = null,Object? notionalUsd = freezed,Object? openedAt = null,Object? profitUsd = freezed,Object? updatedAt = null,Object? usdRate = freezed,Object? multiply = null,Object? priceDirection = null,}) {
  return _then(_PositionModel(
productCategoryId: null == productCategoryId ? _self.productCategoryId : productCategoryId // ignore: cast_nullable_to_non_nullable
as String,positionId: null == positionId ? _self.positionId : positionId // ignore: cast_nullable_to_non_nullable
as String,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,platformName: null == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String,tickerName: null == tickerName ? _self.tickerName : tickerName // ignore: cast_nullable_to_non_nullable
as String,positionType: null == positionType ? _self.positionType : positionType // ignore: cast_nullable_to_non_nullable
as TradeType,openPrice: null == openPrice ? _self.openPrice : openPrice // ignore: cast_nullable_to_non_nullable
as double,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,percentageChange: null == percentageChange ? _self.percentageChange : percentageChange // ignore: cast_nullable_to_non_nullable
as double,stopLoss: null == stopLoss ? _self.stopLoss : stopLoss // ignore: cast_nullable_to_non_nullable
as double,takeProfit: null == takeProfit ? _self.takeProfit : takeProfit // ignore: cast_nullable_to_non_nullable
as double,swap: null == swap ? _self.swap : swap // ignore: cast_nullable_to_non_nullable
as double,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int,marginRate: null == marginRate ? _self.marginRate : marginRate // ignore: cast_nullable_to_non_nullable
as double,tradingAccountNumber: freezed == tradingAccountNumber ? _self.tradingAccountNumber : tradingAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,profitRate: null == profitRate ? _self.profitRate : profitRate // ignore: cast_nullable_to_non_nullable
as double,contractSize: null == contractSize ? _self.contractSize : contractSize // ignore: cast_nullable_to_non_nullable
as double,margin: null == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double,marginAllocation: null == marginAllocation ? _self.marginAllocation : marginAllocation // ignore: cast_nullable_to_non_nullable
as double,productCategory: null == productCategory ? _self.productCategory : productCategory // ignore: cast_nullable_to_non_nullable
as String,commission: null == commission ? _self.commission : commission // ignore: cast_nullable_to_non_nullable
as double,leverage: null == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,productLogoUrl: null == productLogoUrl ? _self.productLogoUrl : productLogoUrl // ignore: cast_nullable_to_non_nullable
as String,notionalValue: null == notionalValue ? _self.notionalValue : notionalValue // ignore: cast_nullable_to_non_nullable
as double,pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as double,assetType: null == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String,sector: null == sector ? _self.sector : sector // ignore: cast_nullable_to_non_nullable
as String,baseCurrency: null == baseCurrency ? _self.baseCurrency : baseCurrency // ignore: cast_nullable_to_non_nullable
as String,grossProfit: null == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as double,minLot: null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,maxLot: null == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double,accountUsdCurrencyPair: freezed == accountUsdCurrencyPair ? _self.accountUsdCurrencyPair : accountUsdCurrencyPair // ignore: cast_nullable_to_non_nullable
as String?,isUpdated: freezed == isUpdated ? _self.isUpdated : isUpdated // ignore: cast_nullable_to_non_nullable
as bool?,marginLevel: null == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double,messageType: null == messageType ? _self.messageType : messageType // ignore: cast_nullable_to_non_nullable
as int,notionalUsd: freezed == notionalUsd ? _self.notionalUsd : notionalUsd // ignore: cast_nullable_to_non_nullable
as double?,openedAt: null == openedAt ? _self.openedAt : openedAt // ignore: cast_nullable_to_non_nullable
as String,profitUsd: freezed == profitUsd ? _self.profitUsd : profitUsd // ignore: cast_nullable_to_non_nullable
as double?,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as String,usdRate: freezed == usdRate ? _self.usdRate : usdRate // ignore: cast_nullable_to_non_nullable
as double?,multiply: null == multiply ? _self.multiply : multiply // ignore: cast_nullable_to_non_nullable
as int,priceDirection: null == priceDirection ? _self.priceDirection : priceDirection // ignore: cast_nullable_to_non_nullable
as PriceDirection,
  ));
}


}

// dart format on

import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/price_direction.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_selected_language_code_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';

part 'position_model.freezed.dart';
part 'position_model.g.dart';

@unfreezed
abstract class PositionModel with _$PositionModel {
  PositionModel._(); // Added private constructor for custom methods

  factory PositionModel({
    @J<PERSON><PERSON><PERSON>(name: 'productCategoryId') required String productCategoryId,
    @J<PERSON><PERSON><PERSON>(name: 'positionId') required String positionId,
    @JsonKey(name: 'profit') double? profit,
    @JsonKey(name: 'symbol') required String platformName,
    @JsonKey(name: 'productName') required String tickerName,
    @JsonKey(name: 'positionType')
    @TradeTypeIntConverter()
    required TradeType positionType,
    @Json<PERSON>ey(name: 'openPrice') required double openPrice,
    @Json<PERSON>ey(name: 'currentPrice') required double currentPrice,
    @JsonKey(name: 'percentageChange') required double percentageChange,
    @JsonKey(name: 'stopLoss') required double stopLoss,
    @JsonKey(name: 'takeProfit') required double takeProfit,
    @JsonKey(name: 'swap') required double swap,
    @JsonKey(name: 'volume') required int volume,
    @JsonKey(name: 'marginRate') required double marginRate,
    @JsonKey(name: 'tradingAccountNumber') String? tradingAccountNumber,
    @JsonKey(name: 'profitRate') required double profitRate,
    @JsonKey(name: 'contractSize') required double contractSize,
    @JsonKey(name: 'margin') required double margin,
    @JsonKey(name: 'marginAllocation') required double marginAllocation,
    @JsonKey(name: 'productCategory') required String productCategory,
    @JsonKey(name: 'commission') required double commission,
    @JsonKey(name: 'leverage') required int leverage,
    @JsonKey(name: 'digits') required int digits,
    @JsonKey(name: 'productLogoUrl') required String productLogoUrl,
    @JsonKey(name: 'notionalValue') required double notionalValue,
    @JsonKey(name: 'pipValue') required double pipValue,
    @JsonKey(name: 'assetType') required String assetType,
    @JsonKey(name: 'sector') required String sector,
    @JsonKey(name: 'baseCurrency') required String baseCurrency,
    @JsonKey(name: 'grossProfit') required double grossProfit,
    @JsonKey(name: 'minLot') required double minLot,
    @JsonKey(name: 'maxLot') required double maxLot,
    @JsonKey(name: 'accountUsdCurrencyPair') String? accountUsdCurrencyPair,
    @JsonKey(name: 'isUpdated') bool? isUpdated,
    @JsonKey(name: 'marginLevel') required double marginLevel,
    @JsonKey(name: 'messageType') required int messageType,
    @JsonKey(name: 'notionalUsd') double? notionalUsd,
    @JsonKey(name: 'openedAt', fromJson: _formatDate) required String openedAt,
    @JsonKey(name: 'profitUsd') double? profitUsd,
    @JsonKey(name: 'updatedAt') required String updatedAt,
    @JsonKey(name: 'usdRate') double? usdRate,
    @JsonKey(name: 'multiply') required int multiply,
    @JsonKey(name: 'priceDirection')
    @PriceDirectionIntConverter()
    required PriceDirection priceDirection,
  }) = _PositionModel;

  double get buyPercentage => ((currentPrice - openPrice) / openPrice) * 100;
  double get sellPercentage => ((openPrice - currentPrice) / openPrice) * 100;
  double get lotSize => volume / 10000;

  factory PositionModel.fromJson(Map<String, dynamic> json) =>
      _$PositionModelFromJson(json);
  double get netProfit => grossProfit + swap + commission;
}

//TODO(Danya): clean this up later on
String _formatDate(String date) {
  try {
    final parsedDate = DateTime.parse(date).toLocal();

    final selectedLanguage =
        diContainer<GetSelectedLanguageCodeUseCase>().call();

    return DateFormat('dd/MM/yyyy HH:mm', selectedLanguage).format(parsedDate);
  } catch (e) {
    print('Date parsing error: $e for date: $date');
    return date;
  }
}

import 'package:e_trader/src/data/socket/position_model.dart';

/// Object pool for managing and reusing PositionModel instances
/// Handles target-specific logic for price position lifecycle management
class PositionModelPool {
  final Map<String, PositionModel> _pool = {};
  final Map<String, Map<String, dynamic>> _lastJsonData = {};

  /// Process position based on target type and return pooled position
  /// Returns deleted position for deleted/triggered targets, pooled position for others
  PositionModel processPosition(
    String? target,
    Map<String, dynamic> positionJson,
  ) {
    final positionId = positionJson['positionId'] as String;

    // Handle null target - Initial data from socket
    if (target == null) {
      return _handleInitialData(positionId, positionJson);
    }

    // Handle delete/trigger targets - remove from pool
    if (target == 'PositionDeleted') {
      final deletedPosition = _pool.remove(positionId);
      _lastJsonData.remove(positionId);
      // If position doesn't exist in pool, create it from JSON for deletion tracking
      return deletedPosition ?? PositionModel.fromJson(positionJson);
    }

    // Handle add/update targets
    return _handleAddOrUpdate(positionId, positionJson);
  }

  /// Handle initial data loading (target is null)
  /// Always create/replace the position to ensure latest state from server
  PositionModel _handleInitialData(
    String positionId,
    Map<String, dynamic> positionJson,
  ) {
    final position = PositionModel.fromJson(positionJson);
    _pool[positionId] = position;
    _lastJsonData[positionId] = Map<String, dynamic>.from(positionJson);
    return position;
  }

  /// Handle add/update operations with change detection
  PositionModel _handleAddOrUpdate(
    String positionId,
    Map<String, dynamic> positionJson,
  ) {
    final existingPosition = _pool[positionId];
    final lastJson = _lastJsonData[positionId];

    if (existingPosition != null && lastJson != null) {
      if (_hasDataChanged(lastJson, positionJson)) {
        // Data changed, update existing position properties in place
        _updatepositionInPlace(existingPosition, positionJson);
        _lastJsonData[positionId] = Map<String, dynamic>.from(positionJson);
      }
      // Return the same instance (either updated or unchanged)
      return existingPosition;
    }

    // No existing position, create new one
    final newPosition = PositionModel.fromJson(positionJson);
    _pool[positionId] = newPosition;
    _lastJsonData[positionId] = Map<String, dynamic>.from(positionJson);
    return newPosition;
  }

  /// Update position properties in place to avoid creating new instances
  void _updatepositionInPlace(
    PositionModel position,
    Map<String, dynamic> json,
  ) {
    // Update properties that can change during position lifecycle
    final newProfit = (json['profit'] as num?)?.toDouble();
    final newGrossProfit = (json['grossProfit'] as num?)?.toDouble();
    final newcurrentPrice = (json['currentPrice'] as num?)?.toDouble();
    final newMarginRate = (json['marginRate'] as num?)?.toDouble();
    final newProfitRate = (json['profitRate'] as num?)?.toDouble();
    final newPipValue = (json['pipValue'] as num?)?.toDouble();
    // final newExposure = (json['exposure'] as num?)?.toDouble();

    // Update properties directly (now possible with @unfreezed)
    if (newProfit != null) position.profit = newProfit;
    if (newGrossProfit != null) position.grossProfit = newGrossProfit;
    if (newcurrentPrice != null) position.currentPrice = newcurrentPrice;
    if (newMarginRate != null) position.marginRate = newMarginRate;
    if (newProfitRate != null) position.profitRate = newProfitRate;
    if (newPipValue != null) position.pipValue = newPipValue;
  }

  /// Check if the JSON data has changed for important fields
  /// Only checks fields that matter for updates
  bool _hasDataChanged(
    Map<String, dynamic> oldJson,
    Map<String, dynamic> newJson,
  ) {
    // List of fields that matter for updates
    const importantFields = [
      'profit',
      'grossProfit',
      'currentPrice',
      'marginRate',
      'profitRate',
      'pipValue',
      'exposure',
    ];

    for (final field in importantFields) {
      if (oldJson[field] != newJson[field]) {
        return true;
      }
    }
    return false;
  }

  /// Check if position exists in pool
  bool hasposition(String positionId) => _pool.containsKey(positionId);

  /// Get existing position without creating new one
  PositionModel? getExisting(String positionId) => _pool[positionId];

  /// Remove position from pool (for cleanup when unsubscribing)
  void removePosition(String positionId) {
    _pool.remove(positionId);
    _lastJsonData.remove(positionId);
  }

  /// Clear entire pool (for cleanup)
  void clear() {
    _pool.clear();
    _lastJsonData.clear();
  }

  /// Get pool size for monitoring
  int get size => _pool.length;

  /// Get all position IDs in pool
  Set<String> get positionIds => _pool.keys.toSet();

  /// Get all positions in pool
  List<PositionModel> get allPositions => _pool.values.toList();
}

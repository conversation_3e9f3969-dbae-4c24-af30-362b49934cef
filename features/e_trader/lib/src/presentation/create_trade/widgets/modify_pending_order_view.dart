import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_pending_order/bloc/modify_pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/modify_pending_order_loading.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_limit_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart';
import 'package:e_trader/src/presentation/positions_and_trades/order_list_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ModifyPendingOrderView extends StatelessWidget {
  final OrderModel order;
  const ModifyPendingOrderView({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    String locale = Localizations.localeOf(context).toString();
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return BlocConsumer<ModifyPendingOrderBloc, ModifyPendingOrderState>(
      listenWhen:
          (previous, current) => previous.currentState != current.currentState,
      listener: (listenerContext, state) {
        if (state.currentState is ModifyPendingOrderSuccessProcessState) {
          DuploToast().showToastMessage(
            context: listenerContext,
            widget: DuploToastTrade(
              titleMessage:
                  state.orderModel.tradeType == TradeType.buy
                      ? localization.trader_buyOrderModified
                      : localization.trader_sellOrderModified,
              trade: TradeToastModel(
                symbolImage: state.orderModel.productLogoUrl,
                symbolName: state.orderModel.tickerName,
                lotSize: EquitiFormatter.formatNumber(
                  value: state.orderModel.lotSize,
                  locale: locale,
                ),
                price: EquitiFormatter.decimalPatternDigits(
                  value: state.orderModel.openPrice,
                  digits: order.digits,
                  locale: locale,
                ),
                type:
                    state.orderModel.tradeType == TradeType.buy
                        ? TradeToastType.buy
                        : TradeToastType.sell,
              ),
              type: ToastMessageType.success,
              onLeadingAction: () {
                DuploToast().hidesToastMessage();
                if (listenerContext.mounted) Navigator.pop(listenerContext);
              },
            ),
          );
          Navigator.pop(listenerContext);
        } else if (state.currentState is OrderDeletionSuccessProcessState) {
          DuploToast().showToastMessage(
            context: listenerContext,
            widget: DuploToastTrade(
              titleMessage: localization.trader_orderDeleted,
              trade: TradeToastModel(
                symbolImage: state.orderModel.productLogoUrl,
                symbolName: state.orderModel.tickerName,
                lotSize: EquitiFormatter.formatNumber(
                  value: state.orderModel.lotSize,
                  locale: locale,
                ),
                price: EquitiFormatter.decimalPatternDigits(
                  value: state.orderModel.currentPrice,
                  digits: order.digits,
                  locale: locale,
                ),
                type:
                    state.orderModel.tradeType == TradeType.buy
                        ? TradeToastType.buy
                        : TradeToastType.sell,
              ),
              type: ToastMessageType.success,
              onLeadingAction: () {
                DuploToast().hidesToastMessage();
                if (listenerContext.mounted) Navigator.pop(listenerContext);
              },
            ),
          );
          Navigator.pop(listenerContext);
        } else if (state.currentState is OrderDeletionFailureProcessState) {
          DuploToast().showToastMessage(
            context: listenerContext,
            widget: DuploToastMessage(
              titleMessage: localization.trader_deleteOrderFailed,
              descriptionMessage: localization.trader_somethingWentWrong,
              messageType: ToastMessageType.error,
              onLeadingAction: () {
                DuploToast().hidesToastMessage();
              },
            ),
          );
        } else if (state.currentState
            is OrderDeletionMarketClosedProcessState) {
          DuploToast().showToastMessage(
            context: context,
            widget: DuploToastMessage(
              titleMessage: localization.trader_marketIsClosed,
              descriptionMessage:
                  localization
                      .trader_modifyPendingOrder_marketIsClosedDescription,
              messageType: ToastMessageType.error,
              onLeadingAction: () => DuploToast().hidesToastMessage(),
            ),
          );
        } else if (state.currentState is ModifyPendingOrderErrorProcessState) {
          DuploToast().showToastMessage(
            context: context,
            widget: DuploToastMessage(
              titleMessage: localization.trader_tryAgain,
              descriptionMessage: localization.trader_somethingWentWrong,
              messageType: ToastMessageType.error,
              onLeadingAction: () => DuploToast().hidesToastMessage(),
            ),
          );
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return switch (state.currentState) {
          ModifyPendingOrderLoadingProcessState() =>
            ModifyPendingOrderLoading(),
          ModifyPendingOrderDisconnectedProcessState() => Center(
            child: Text(localization.trader_somethingWentWrong),
          ),
          _ => ListView(
            children: [
              OrderListTile(
                tpValue: order.takeProfit,
                slValue: order.stopLoss,
                digits: order.digits,
                orderPrice: order.openPrice,
                lots: order.lotSize,
                currentPrice: state.orderModel.currentPrice,
                tradeType: order.tradeType,
                productIconURL: order.productLogoUrl,
                productName: order.tickerName,
                entryOrderType: order.entryOrderType,
                priceChange: order.priceChange,
              ),
              Divider(color: theme.border.borderSecondary, height: 0),
              Container(
                decoration: BoxDecoration(
                  color: theme.background.bgPrimary,
                  border: Border(
                    left:
                        isRTL
                            ? BorderSide.none
                            : BorderSide(
                              color:
                                  order.tradeType == TradeType.buy
                                      ? theme.foreground.fgSuccessPrimary
                                      : theme.foreground.fgErrorPrimary,
                              width: 4.0,
                            ),
                    right:
                        isRTL
                            ? BorderSide(
                              color:
                                  order.tradeType == TradeType.buy
                                      ? theme.foreground.fgSuccessPrimary
                                      : theme.foreground.fgErrorPrimary,
                              width: 4.0,
                            )
                            : BorderSide.none,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsetsDirectional.all(16),
                  child: DuploKeyValueDisplay(
                    keyTextStyle: duploTextStyles.textXs,
                    valueTextStyle: duploTextStyles.textXs,
                    contentSpacing: 0.0,
                    addBorder: false,
                    keyColor: theme.text.textTertiary,
                    valueFontWeight: DuploFontWeight.regular,
                    keyFontWeight: DuploFontWeight.regular,
                    keyValuePairs: [
                      KeyValuePair(
                        label: localization.trader_openPriceTitle,
                        value: EquitiFormatter.formatTradePrice(
                          value: order.openPrice,
                          digits: order.digits,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: localization.trader_currentPriceTitle,
                        value: EquitiFormatter.formatTradePrice(
                          value: state.orderModel.currentPrice,
                          digits: order.digits,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: localization.trader_takeProfitTitle,
                        value: EquitiFormatter.formatTradePrice(
                          value: order.takeProfit,
                          digits: order.digits,
                          locale: locale,
                        ),
                      ),
                      KeyValuePair(
                        label: localization.trader_stopLossTitle,
                        value: EquitiFormatter.formatTradePrice(
                          value: order.stopLoss,
                          digits: order.digits,
                          locale: locale,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8.0),
              Padding(
                padding: EdgeInsetsDirectional.only(start: 24.0, end: 16.0),
                child: DuploText(
                  text: localization.trader_modifyOrder,
                  textAlign: TextAlign.left,
                  color: theme.text.textPrimary,
                  style: context.duploTextStyles.textLg,
                  maxLines: 1,
                  fontWeight: DuploFontWeight.semiBold,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.symmetric(
                  vertical: 8.0,
                  horizontal: 16.0,
                ),
                child: _ModifyPendingOrderForm(order: order),
              ),
            ],
          ),
        };
      },
    );
  }
}

class _ModifyPendingOrderForm extends StatelessWidget {
  const _ModifyPendingOrderForm({required this.order});

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocBuilder<ModifyPendingOrderBloc, ModifyPendingOrderState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        return Card(
          elevation: 0,
          color: theme.background.bgPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
            side: BorderSide(color: theme.border.borderSecondary, width: 1.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                OrderPriceWidget(
                  args: (
                    initialPrice: order.openPrice,
                    currentPrice: state.orderModel.currentPrice,
                    digits: order.digits,
                    isDisabled: false,
                  ),
                  onOrderPriceChanged:
                      (orderPriceState) =>
                          blocBuilderContext.read<ModifyPendingOrderBloc>().add(
                            ModifyPendingOrderEvent.priceChanged(
                              orderPriceState,
                            ),
                          ),
                  tradeType: order.tradeType,
                ),
                const SizedBox(height: 16.0),
                if (state.orderPriceState.isValid())
                  OrderLimitWidget(
                    orderType: OrderType.pendingOrder,
                    isEnabled: order.takeProfit != 0.0,
                    isModifyTrade: true,
                    orderLimitType: OrderLimitType.takeProfit,
                    digits: order.digits,
                    tradeType: order.tradeType,
                    pipValue: state.marginInformation!.pipInformation.pipValue,
                    pipMultipler:
                        state.marginInformation!.pipInformation.pipMultipler,
                    pipSize: state.marginInformation!.pipInformation.onePip,
                    currentPrice: state.orderPriceState.value,
                    initialPrice: order.takeProfit,
                    methodOrder: [
                      MethodTypeEnum.distance,
                      MethodTypeEnum.price,
                      MethodTypeEnum.profitOrLoss,
                    ],
                    onOrderLimitStateChanged:
                        (orderLimitState) => blocBuilderContext
                            .read<ModifyPendingOrderBloc>()
                            .add(
                              ModifyPendingOrderEvent.takeProfitChanged(
                                orderLimitState,
                              ),
                            ),
                  ),
                const SizedBox(height: 8.0),
                Divider(color: theme.border.borderSecondary),
                const SizedBox(height: 8.0),
                if (state.orderPriceState.isValid())
                  OrderLimitWidget(
                    orderType: OrderType.pendingOrder,
                    isModifyTrade: true,
                    isEnabled: order.stopLoss != 0.0,
                    orderLimitType: OrderLimitType.stopLoss,
                    digits: order.digits,
                    tradeType: order.tradeType,
                    pipValue: state.marginInformation!.pipInformation.pipValue,
                    pipMultipler:
                        state.marginInformation!.pipInformation.pipMultipler,
                    pipSize: state.marginInformation!.pipInformation.onePip,
                    currentPrice: state.orderPriceState.value,
                    initialPrice: order.stopLoss,
                    methodOrder: [
                      MethodTypeEnum.distance,
                      MethodTypeEnum.price,
                      MethodTypeEnum.profitOrLoss,
                    ],
                    onOrderLimitStateChanged: (orderLimitState) {
                      blocBuilderContext.read<ModifyPendingOrderBloc>().add(
                        ModifyPendingOrderEvent.stopLossChanged(
                          orderLimitState,
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_limit_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OrderLimitEvent implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderLimitEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent()';
}


}

/// @nodoc
class $OrderLimitEventCopyWith<$Res>  {
$OrderLimitEventCopyWith(OrderLimitEvent _, $Res Function(OrderLimitEvent) __);
}


/// @nodoc


class _OrderLimitInitialize with DiagnosticableTreeMixin implements OrderLimitEvent {
  const _OrderLimitInitialize({required this.isEnabled});
  

 final  bool isEnabled;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitInitializeCopyWith<_OrderLimitInitialize> get copyWith => __$OrderLimitInitializeCopyWithImpl<_OrderLimitInitialize>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent.initialize'))
    ..add(DiagnosticsProperty('isEnabled', isEnabled));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitInitialize&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled));
}


@override
int get hashCode => Object.hash(runtimeType,isEnabled);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent.initialize(isEnabled: $isEnabled)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitInitializeCopyWith<$Res> implements $OrderLimitEventCopyWith<$Res> {
  factory _$OrderLimitInitializeCopyWith(_OrderLimitInitialize value, $Res Function(_OrderLimitInitialize) _then) = __$OrderLimitInitializeCopyWithImpl;
@useResult
$Res call({
 bool isEnabled
});




}
/// @nodoc
class __$OrderLimitInitializeCopyWithImpl<$Res>
    implements _$OrderLimitInitializeCopyWith<$Res> {
  __$OrderLimitInitializeCopyWithImpl(this._self, this._then);

  final _OrderLimitInitialize _self;
  final $Res Function(_OrderLimitInitialize) _then;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isEnabled = null,}) {
  return _then(_OrderLimitInitialize(
isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _OrderLimitMethodChanged with DiagnosticableTreeMixin implements OrderLimitEvent {
  const _OrderLimitMethodChanged(this.methodType);
  

 final  MethodType methodType;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitMethodChangedCopyWith<_OrderLimitMethodChanged> get copyWith => __$OrderLimitMethodChangedCopyWithImpl<_OrderLimitMethodChanged>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent.methodChanged'))
    ..add(DiagnosticsProperty('methodType', methodType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitMethodChanged&&(identical(other.methodType, methodType) || other.methodType == methodType));
}


@override
int get hashCode => Object.hash(runtimeType,methodType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent.methodChanged(methodType: $methodType)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitMethodChangedCopyWith<$Res> implements $OrderLimitEventCopyWith<$Res> {
  factory _$OrderLimitMethodChangedCopyWith(_OrderLimitMethodChanged value, $Res Function(_OrderLimitMethodChanged) _then) = __$OrderLimitMethodChangedCopyWithImpl;
@useResult
$Res call({
 MethodType methodType
});


$MethodTypeCopyWith<$Res> get methodType;

}
/// @nodoc
class __$OrderLimitMethodChangedCopyWithImpl<$Res>
    implements _$OrderLimitMethodChangedCopyWith<$Res> {
  __$OrderLimitMethodChangedCopyWithImpl(this._self, this._then);

  final _OrderLimitMethodChanged _self;
  final $Res Function(_OrderLimitMethodChanged) _then;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? methodType = null,}) {
  return _then(_OrderLimitMethodChanged(
null == methodType ? _self.methodType : methodType // ignore: cast_nullable_to_non_nullable
as MethodType,
  ));
}

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MethodTypeCopyWith<$Res> get methodType {
  
  return $MethodTypeCopyWith<$Res>(_self.methodType, (value) {
    return _then(_self.copyWith(methodType: value));
  });
}
}

/// @nodoc


class _OrderLimitInputChanged with DiagnosticableTreeMixin implements OrderLimitEvent {
  const _OrderLimitInputChanged({required this.input});
  

 final  String input;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitInputChangedCopyWith<_OrderLimitInputChanged> get copyWith => __$OrderLimitInputChangedCopyWithImpl<_OrderLimitInputChanged>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent.inputChanged'))
    ..add(DiagnosticsProperty('input', input));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitInputChanged&&(identical(other.input, input) || other.input == input));
}


@override
int get hashCode => Object.hash(runtimeType,input);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent.inputChanged(input: $input)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitInputChangedCopyWith<$Res> implements $OrderLimitEventCopyWith<$Res> {
  factory _$OrderLimitInputChangedCopyWith(_OrderLimitInputChanged value, $Res Function(_OrderLimitInputChanged) _then) = __$OrderLimitInputChangedCopyWithImpl;
@useResult
$Res call({
 String input
});




}
/// @nodoc
class __$OrderLimitInputChangedCopyWithImpl<$Res>
    implements _$OrderLimitInputChangedCopyWith<$Res> {
  __$OrderLimitInputChangedCopyWithImpl(this._self, this._then);

  final _OrderLimitInputChanged _self;
  final $Res Function(_OrderLimitInputChanged) _then;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? input = null,}) {
  return _then(_OrderLimitInputChanged(
input: null == input ? _self.input : input // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OrderLimitValuesChanged with DiagnosticableTreeMixin implements OrderLimitEvent {
  const _OrderLimitValuesChanged({required this.config, required this.input});
  

 final  OrderLimitConfig config;
 final  String input;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitValuesChangedCopyWith<_OrderLimitValuesChanged> get copyWith => __$OrderLimitValuesChangedCopyWithImpl<_OrderLimitValuesChanged>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent.valuesChanged'))
    ..add(DiagnosticsProperty('config', config))..add(DiagnosticsProperty('input', input));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitValuesChanged&&(identical(other.config, config) || other.config == config)&&(identical(other.input, input) || other.input == input));
}


@override
int get hashCode => Object.hash(runtimeType,config,input);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent.valuesChanged(config: $config, input: $input)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitValuesChangedCopyWith<$Res> implements $OrderLimitEventCopyWith<$Res> {
  factory _$OrderLimitValuesChangedCopyWith(_OrderLimitValuesChanged value, $Res Function(_OrderLimitValuesChanged) _then) = __$OrderLimitValuesChangedCopyWithImpl;
@useResult
$Res call({
 OrderLimitConfig config, String input
});


$OrderLimitConfigCopyWith<$Res> get config;

}
/// @nodoc
class __$OrderLimitValuesChangedCopyWithImpl<$Res>
    implements _$OrderLimitValuesChangedCopyWith<$Res> {
  __$OrderLimitValuesChangedCopyWithImpl(this._self, this._then);

  final _OrderLimitValuesChanged _self;
  final $Res Function(_OrderLimitValuesChanged) _then;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? config = null,Object? input = null,}) {
  return _then(_OrderLimitValuesChanged(
config: null == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as OrderLimitConfig,input: null == input ? _self.input : input // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderLimitConfigCopyWith<$Res> get config {
  
  return $OrderLimitConfigCopyWith<$Res>(_self.config, (value) {
    return _then(_self.copyWith(config: value));
  });
}
}

/// @nodoc


class _OrderLimitEnabled with DiagnosticableTreeMixin implements OrderLimitEvent {
  const _OrderLimitEnabled({required this.isEnabled, required this.input});
  

 final  bool isEnabled;
 final  String input;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitEnabledCopyWith<_OrderLimitEnabled> get copyWith => __$OrderLimitEnabledCopyWithImpl<_OrderLimitEnabled>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitEvent.orderLimitEnabled'))
    ..add(DiagnosticsProperty('isEnabled', isEnabled))..add(DiagnosticsProperty('input', input));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitEnabled&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.input, input) || other.input == input));
}


@override
int get hashCode => Object.hash(runtimeType,isEnabled,input);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitEvent.orderLimitEnabled(isEnabled: $isEnabled, input: $input)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitEnabledCopyWith<$Res> implements $OrderLimitEventCopyWith<$Res> {
  factory _$OrderLimitEnabledCopyWith(_OrderLimitEnabled value, $Res Function(_OrderLimitEnabled) _then) = __$OrderLimitEnabledCopyWithImpl;
@useResult
$Res call({
 bool isEnabled, String input
});




}
/// @nodoc
class __$OrderLimitEnabledCopyWithImpl<$Res>
    implements _$OrderLimitEnabledCopyWith<$Res> {
  __$OrderLimitEnabledCopyWithImpl(this._self, this._then);

  final _OrderLimitEnabled _self;
  final $Res Function(_OrderLimitEnabled) _then;

/// Create a copy of OrderLimitEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isEnabled = null,Object? input = null,}) {
  return _then(_OrderLimitEnabled(
isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,input: null == input ? _self.input : input // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$OrderLimitState implements DiagnosticableTreeMixin {

 OrderLimitConfig get config; MethodType get selectedMethodType; double get currentPrice; OrderLimitCalculation get calculations; TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get processState;
/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderLimitStateCopyWith<OrderLimitState> get copyWith => _$OrderLimitStateCopyWithImpl<OrderLimitState>(this as OrderLimitState, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitState'))
    ..add(DiagnosticsProperty('config', config))..add(DiagnosticsProperty('selectedMethodType', selectedMethodType))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('calculations', calculations))..add(DiagnosticsProperty('processState', processState));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderLimitState&&(identical(other.config, config) || other.config == config)&&(identical(other.selectedMethodType, selectedMethodType) || other.selectedMethodType == selectedMethodType)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.calculations, calculations) || other.calculations == calculations)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,config,selectedMethodType,currentPrice,calculations,processState);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitState(config: $config, selectedMethodType: $selectedMethodType, currentPrice: $currentPrice, calculations: $calculations, processState: $processState)';
}


}

/// @nodoc
abstract mixin class $OrderLimitStateCopyWith<$Res>  {
  factory $OrderLimitStateCopyWith(OrderLimitState value, $Res Function(OrderLimitState) _then) = _$OrderLimitStateCopyWithImpl;
@useResult
$Res call({
 OrderLimitConfig config, MethodType selectedMethodType, double currentPrice, OrderLimitCalculation calculations, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> processState
});


$OrderLimitConfigCopyWith<$Res> get config;$MethodTypeCopyWith<$Res> get selectedMethodType;$OrderLimitCalculationCopyWith<$Res> get calculations;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get processState;

}
/// @nodoc
class _$OrderLimitStateCopyWithImpl<$Res>
    implements $OrderLimitStateCopyWith<$Res> {
  _$OrderLimitStateCopyWithImpl(this._self, this._then);

  final OrderLimitState _self;
  final $Res Function(OrderLimitState) _then;

/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? config = null,Object? selectedMethodType = null,Object? currentPrice = null,Object? calculations = null,Object? processState = null,}) {
  return _then(_self.copyWith(
config: null == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as OrderLimitConfig,selectedMethodType: null == selectedMethodType ? _self.selectedMethodType : selectedMethodType // ignore: cast_nullable_to_non_nullable
as MethodType,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,calculations: null == calculations ? _self.calculations : calculations // ignore: cast_nullable_to_non_nullable
as OrderLimitCalculation,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}
/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderLimitConfigCopyWith<$Res> get config {
  
  return $OrderLimitConfigCopyWith<$Res>(_self.config, (value) {
    return _then(_self.copyWith(config: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MethodTypeCopyWith<$Res> get selectedMethodType {
  
  return $MethodTypeCopyWith<$Res>(_self.selectedMethodType, (value) {
    return _then(_self.copyWith(selectedMethodType: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderLimitCalculationCopyWith<$Res> get calculations {
  
  return $OrderLimitCalculationCopyWith<$Res>(_self.calculations, (value) {
    return _then(_self.copyWith(calculations: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get processState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _OrderLimitState with DiagnosticableTreeMixin implements OrderLimitState {
  const _OrderLimitState({required this.config, required this.selectedMethodType, required this.currentPrice, required this.calculations, required this.processState});
  

@override final  OrderLimitConfig config;
@override final  MethodType selectedMethodType;
@override final  double currentPrice;
@override final  OrderLimitCalculation calculations;
@override final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> processState;

/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderLimitStateCopyWith<_OrderLimitState> get copyWith => __$OrderLimitStateCopyWithImpl<_OrderLimitState>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderLimitState'))
    ..add(DiagnosticsProperty('config', config))..add(DiagnosticsProperty('selectedMethodType', selectedMethodType))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('calculations', calculations))..add(DiagnosticsProperty('processState', processState));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderLimitState&&(identical(other.config, config) || other.config == config)&&(identical(other.selectedMethodType, selectedMethodType) || other.selectedMethodType == selectedMethodType)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.calculations, calculations) || other.calculations == calculations)&&(identical(other.processState, processState) || other.processState == processState));
}


@override
int get hashCode => Object.hash(runtimeType,config,selectedMethodType,currentPrice,calculations,processState);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderLimitState(config: $config, selectedMethodType: $selectedMethodType, currentPrice: $currentPrice, calculations: $calculations, processState: $processState)';
}


}

/// @nodoc
abstract mixin class _$OrderLimitStateCopyWith<$Res> implements $OrderLimitStateCopyWith<$Res> {
  factory _$OrderLimitStateCopyWith(_OrderLimitState value, $Res Function(_OrderLimitState) _then) = __$OrderLimitStateCopyWithImpl;
@override @useResult
$Res call({
 OrderLimitConfig config, MethodType selectedMethodType, double currentPrice, OrderLimitCalculation calculations, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> processState
});


@override $OrderLimitConfigCopyWith<$Res> get config;@override $MethodTypeCopyWith<$Res> get selectedMethodType;@override $OrderLimitCalculationCopyWith<$Res> get calculations;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get processState;

}
/// @nodoc
class __$OrderLimitStateCopyWithImpl<$Res>
    implements _$OrderLimitStateCopyWith<$Res> {
  __$OrderLimitStateCopyWithImpl(this._self, this._then);

  final _OrderLimitState _self;
  final $Res Function(_OrderLimitState) _then;

/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? config = null,Object? selectedMethodType = null,Object? currentPrice = null,Object? calculations = null,Object? processState = null,}) {
  return _then(_OrderLimitState(
config: null == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as OrderLimitConfig,selectedMethodType: null == selectedMethodType ? _self.selectedMethodType : selectedMethodType // ignore: cast_nullable_to_non_nullable
as MethodType,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,calculations: null == calculations ? _self.calculations : calculations // ignore: cast_nullable_to_non_nullable
as OrderLimitCalculation,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderLimitConfigCopyWith<$Res> get config {
  
  return $OrderLimitConfigCopyWith<$Res>(_self.config, (value) {
    return _then(_self.copyWith(config: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MethodTypeCopyWith<$Res> get selectedMethodType {
  
  return $MethodTypeCopyWith<$Res>(_self.selectedMethodType, (value) {
    return _then(_self.copyWith(selectedMethodType: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderLimitCalculationCopyWith<$Res> get calculations {
  
  return $OrderLimitCalculationCopyWith<$Res>(_self.calculations, (value) {
    return _then(_self.copyWith(calculations: value));
  });
}/// Create a copy of OrderLimitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get processState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

// dart format on

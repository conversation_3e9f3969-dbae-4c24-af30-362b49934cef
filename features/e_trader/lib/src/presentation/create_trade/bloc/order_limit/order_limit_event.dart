part of 'order_limit_bloc.dart';

@freezed
sealed class OrderLimitEvent with _$OrderLimitEvent {
  const factory OrderLimitEvent.initialize({required bool isEnabled}) =
      _OrderLimitInitialize;
  const factory OrderLimitEvent.methodChanged(MethodType methodType) =
      _OrderLimitMethodChanged;

  const factory OrderLimitEvent.inputChanged({required String input}) =
      _OrderLimitInputChanged;

  const factory OrderLimitEvent.valuesChanged({
    required OrderLimitConfig config,
    required String input,
  }) = _OrderLimitValuesChanged;

  const factory OrderLimitEvent.orderLimitEnabled({
    required bool isEnabled,
    required String input,
  }) = _OrderLimitEnabled;
}

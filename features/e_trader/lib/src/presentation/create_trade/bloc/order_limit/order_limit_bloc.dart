import 'dart:async';

import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/domain/model/method_type.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/model/order_limit_config.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_limit_bloc.freezed.dart';
part 'order_limit_event.dart';
part 'order_limit_state.dart';

class OrderLimitBloc extends Bloc<OrderLimitEvent, OrderLimitState> {
  OrderLimitBloc({required OrderLimitConfig config, required bool isEnabled})
    : super(
        OrderLimitState(
          config: config,
          selectedMethodType: config.methods.firstOrNull!,
          currentPrice: config.currentPrice.toDouble(),
          calculations: config.initialCalculation(),
          processState: TradeComponentState.inactive(),
        ),
      ) {
    on<OrderLimitEvent>(
      (event, emit) => switch (event) {
        _OrderLimitInitialize initializeEvent => _handleInitialize(
          initializeEvent,
          emit,
        ),
        _OrderLimitMethodChanged methodChangedEvent => _handleMethodChanged(
          methodChangedEvent,
          emit,
        ),
        _OrderLimitInputChanged inputChangedEvent => _handleInputChanged(
          inputChangedEvent,
          emit,
        ),
        _OrderLimitValuesChanged valuesChangedEvent => _handleValueChanged(
          valuesChangedEvent,
          emit,
        ),
        _OrderLimitEnabled orderLimitEnabledEvent => _handleEnabled(
          orderLimitEnabledEvent,
          emit,
        ),
      },
      transformer: sequential(),
    );
    add(OrderLimitEvent.initialize(isEnabled: isEnabled));
  }

  void _handleInitialize(
    _OrderLimitInitialize event,
    Emitter<OrderLimitState> emit,
  ) {
    emit(
      state.copyWith(
        processState:
            event.isEnabled
                ? TradeComponentState.success(state.calculations)
                : TradeComponentState.inactive(),
      ),
    );
  }

  void _handleMethodChanged(
    _OrderLimitMethodChanged event,
    Emitter<OrderLimitState> emit,
  ) {
    final errorCode = switch (event.methodType) {
      DistanceMethod method => method.isValid(
        input: state.calculations.distance,
      ),
      PriceMethod method => method.isValid(input: state.calculations.price),
      ProfitOrLossMethod method => method.isValid(
        input: state.calculations.profitOrLoss,
      ),
    };
    final calculations = event.methodType.calculate(
      distance: state.calculations.distance,
      limitPrice: state.calculations.price,
      profitOrLoss: state.calculations.profitOrLoss,
    );
    emit(
      state.copyWith(
        selectedMethodType: event.methodType,
        processState:
            errorCode == null
                ? TradeComponentState.success(calculations)
                : TradeComponentState.error(errorCode),
      ),
    );
  }

  void _handleInputChanged(
    _OrderLimitInputChanged event,
    Emitter<OrderLimitState> emit,
  ) {
    // TODO(sagar): handle this in formatter
    final input = Decimal.tryParse(event.input);
    if (input == null) {
      emit(
        state.copyWith(
          processState: const TradeComponentState.error(
            OrderLimitErrorCode.invalidInput,
          ),
        ),
      );
      return;
    }

    final selectedMethod = state.selectedMethodType;
    final errorCode = selectedMethod.isValid(input: input);

    final calculation = switch (selectedMethod) {
      DistanceMethod method => method.calculate(
        profitOrLoss: state.calculations.profitOrLoss,
        distance: input,
        limitPrice: state.calculations.price,
      ),
      PriceMethod method => method.calculate(
        profitOrLoss: state.calculations.profitOrLoss,
        distance: state.calculations.distance,
        limitPrice: input,
      ),
      ProfitOrLossMethod method => method.calculate(
        limitPrice: state.calculations.price,
        distance: state.calculations.distance,
        profitOrLoss: input,
      ),
    };

    emit(
      state.copyWith(
        processState:
            errorCode == null
                ? TradeComponentState.success(calculation)
                : TradeComponentState.error(errorCode),
        calculations: calculation,
      ),
    );
  }

  void _handleValueChanged(
    _OrderLimitValuesChanged event,
    Emitter<OrderLimitState> emit,
  ) {
    if (!state.processState.isActive()) {
      emit(state.copyWith(config: event.config));
      return;
    }
    final input = Decimal.tryParse(event.input);
    if (input == null) {
      emit(
        state.copyWith(
          processState: const TradeComponentState.error(
            OrderLimitErrorCode.invalidInput,
          ),
        ),
      );
      return;
    }
    final selectedMethod = event.config.methods.firstWhere((method) {
      return (method.runtimeType == state.selectedMethodType.runtimeType) &&
          method != state.selectedMethodType;
    }, orElse: () => state.selectedMethodType);

    final errorCode = switch (selectedMethod) {
      DistanceMethod method => method.isValid(
        input: state.calculations.distance,
      ),
      PriceMethod method => method.isValid(input: state.calculations.price),
      ProfitOrLossMethod method => method.isValid(
        input: state.calculations.profitOrLoss,
      ),
    };
    final calculations = selectedMethod.calculate(
      distance: state.calculations.distance,
      limitPrice: state.calculations.price,
      profitOrLoss: state.calculations.profitOrLoss,
    );
    emit(
      state.copyWith(
        config: event.config,
        selectedMethodType: selectedMethod,
        calculations: calculations,
        processState:
            errorCode == null
                ? TradeComponentState.success(calculations)
                : TradeComponentState.error(errorCode),
      ),
    );
  }

  FutureOr<void> _handleEnabled(
    _OrderLimitEnabled event,
    Emitter<OrderLimitState> emit,
  ) {
    if (!event.isEnabled) {
      emit(state.copyWith(processState: TradeComponentState.inactive()));
    } else {
      emit(state.copyWith(processState: TradeComponentState.loading()));
      add(
        OrderLimitEvent.valuesChanged(config: state.config, input: event.input),
      );
    }
  }
}

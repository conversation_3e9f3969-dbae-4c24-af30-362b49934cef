part of 'category_symbols_bloc.dart';

@freezed
sealed class SymbolsEvent with _$SymbolsEvent {
  const factory SymbolsEvent.onGetSymbols({String? query}) = _OnGetSymbols;

  const factory SymbolsEvent.processSymbolPriceResult({
    required SymbolQuoteModel result,
  }) = _ProcessSymbolPriceResult;
  const factory SymbolsEvent.onSortSymbols({
    @Default(SortOrderOptions.defaultOption) SortOrder sortOrder,
  }) = _OnSortSymbols;

  const factory SymbolsEvent.onPriceViewChange({
    @Default(SymbolPriceInfoViewTypeOptions.defaultOption)
    SymbolPriceInfoViewType viewType,
  }) = _OnPriceViewChange;

  const factory SymbolsEvent.onCategorySelected({required String categoryID}) =
      _OnCategorySelected;
  const factory SymbolsEvent.gotoDetails({
    required SymbolDetailViewModel symbolDetail,
    TradeType? tradeDirection,
  }) = _OnGoToDetails;
}

enum SubscriptionStatus { pause, resume }

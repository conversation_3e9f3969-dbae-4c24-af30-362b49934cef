import 'dart:async';

import 'package:e_trader/src/data/api/linked_symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_symbols_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/watchlist_local_cache_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/symbol_quote_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbol_view/symbol_view_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'category_symbols_bloc.freezed.dart';
part 'symbols_event.dart';
part 'symbols_state.dart';

class CategorySymbolsBloc extends Bloc<SymbolsEvent, SymbolsState>
    with DisposableMixin {
  CategorySymbolsBloc(
    this._getSymbolsUseCase,
    this._symbolLocalDataUseCase,
    this._subscribeToSymbolsQuotesUseCase,
    this._getAccountNumberUseCase,
    this._navigation,
    this._symbolViewCubit,
    String categoryId,
    this._watchlistLocalCacheUseCase,
  ) : super(
        SymbolsState(
          symbolDetailViewModel: {},
          symbolQuoteViewModel: {},
          selectedCategoryID: categoryId,
          sortOrder: _symbolViewCubit.state.sortOrder,
          priceInfoViewType: _symbolViewCubit.state.viewType,
        ),
      ) {
    on<SymbolsEvent>((event, emit) {
      if (event is _OnGetSymbols) {
        return _onGetSymbols(event, emit);
      }
    });

    on<_OnGoToDetails>((event, emit) => _onGoToDetails(event));

    on<_ProcessSymbolPriceResult>(
      (event, emit) => _processSymbolPriceResult(event, emit),
    );

    on<_OnSortSymbols>((event, emit) => _onSortSymbols(event, emit));

    on<_OnPriceViewChange>((event, emit) => _onPriceViewChange(event, emit));

    _symbolViewSubscription = _symbolViewCubit.stream.listen((symbolViewState) {
      // Handle sort order changes
      if (symbolViewState.sortOrder != state.sortOrder) {
        add(SymbolsEvent.onSortSymbols(sortOrder: symbolViewState.sortOrder));
      }

      // Handle view type changes (no data reload needed)
      if (symbolViewState.viewType != state.priceInfoViewType) {
        add(SymbolsEvent.onPriceViewChange(viewType: symbolViewState.viewType));
      }
    });

    add(SymbolsEvent.onGetSymbols());
  }

  final GetSymbolsUseCase _getSymbolsUseCase;
  final SymbolLocalDataUseCase _symbolLocalDataUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final EquitiTraderNavigation _navigation;
  final SubscribeToListOfSymbolQuotesUseCase _subscribeToSymbolsQuotesUseCase;
  final SymbolViewCubit _symbolViewCubit;
  final WatchlistLocalCacheUseCase _watchlistLocalCacheUseCase;

  StreamSubscription<SymbolViewState>? _symbolViewSubscription;

  Future<void> _onGetSymbols(
    _OnGetSymbols event,
    Emitter<SymbolsState> emit,
  ) async {
    if (state.hasReachedMax) return;
    final response =
        await _fetchAndProcessSymbols(event)
            .flatMap(_handlePagination)
            .flatMap(
              (_) => _subscribeToSymbolsQuotesUseCase(
                subscriberId: '${CategorySymbolsBloc}_$hashCode',
              ),
            )
            .run();
    return response.fold(
      (exception) => _handleError(exception, emit),
      (symbolPriceResult) => _handleSuccess(symbolPriceResult, emit),
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _fetchAndProcessSymbols(
    _OnGetSymbols event,
  ) => _getSymbolsUseCase(
    categoryID: state.selectedCategoryID,
    pageNumber: state.currentPage,
    query: event.query,
    sortOrder: state.sortOrder,
  ).chainFirst(_updateWatchlistCache).chainFirst(_updateSymbolsDetail);

  TaskEither<Exception, LinkedSymbolModel> _updateWatchlistCache(
    LinkedSymbolModel linkedSymbols,
  ) {
    for (final symbol in linkedSymbols.symbols) {
      if (symbol.platformName != null) {
        _watchlistLocalCacheUseCase.setWatchlistStatus(
          symbol.platformName!,
          symbol.isWatchlist,
        );
      }
    }
    return TaskEither.of(linkedSymbols);
  }

  TaskEither<Exception, LinkedSymbolModel> _updateSymbolsDetail(
    LinkedSymbolModel linkedSymbols,
  ) {
    final symbols =
        linkedSymbols.symbols
            .where((symbol) => symbol.platformName != null)
            .toList();
    state.setSymbolsCount(linkedSymbols.count);
    if (state.symbolsDetail.isEmpty) {
      _createInitialSymbolsMap(symbols);
    } else {
      _updateExistingSymbolsMap(symbols);
    }

    return TaskEither.of(linkedSymbols);
  }

  void _createInitialSymbolsMap(List<SymbolModel> symbols) {
    final symbolsMap = <String, SymbolDetailViewModel>{
      for (final symbol in symbols)
        symbol.platformName!: SymbolDetailViewModel(
          symbolName: symbol.tickerName!,
          platformName: symbol.platformName!,
          imageURL: symbol.productLogoUrl,
          assetType: symbol.assetType,
          minLot: symbol.minLot,
          maxLot: symbol.maxLot,
          digit: symbol.digits ?? 5,
          isForex: symbol.isForex,
          lotsSteps: symbol.lotsSteps ?? 0,
        ),
    };

    state.setSymbolsDetail(symbolsMap);
  }

  void _updateExistingSymbolsMap(List<SymbolModel> symbols) {
    final existingSymbolsMap = state.symbolsDetail;
    for (final symbol in symbols) {
      final existingSymbol = existingSymbolsMap[symbol.platformName];
      existingSymbolsMap[symbol.platformName!] =
          existingSymbol != null
              ? _updateExistingSymbol(existingSymbol, symbol)
              : _createNewSymbol(symbol);
    }
    state.setSymbolsDetail(existingSymbolsMap);
  }

  SymbolDetailViewModel _updateExistingSymbol(
    SymbolDetailViewModel existing,
    SymbolModel symbol,
  ) {
    return existing
      ..symbolName = symbol.tickerName!
      ..imageURL = symbol.productLogoUrl
      ..assetType = symbol.assetType;
  }

  SymbolDetailViewModel _createNewSymbol(SymbolModel symbol) {
    return SymbolDetailViewModel(
      symbolName: symbol.tickerName!,
      platformName: symbol.platformName!,
      imageURL: symbol.productLogoUrl,
      assetType: symbol.assetType,
      minLot: symbol.minLot,
      maxLot: symbol.maxLot,
      digit: symbol.digits ?? 5,
      isForex: symbol.isForex,
      lotsSteps: symbol.lotsSteps ?? 0,
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _handlePagination(
    LinkedSymbolModel linkedSymbols,
  ) {
    if (state.symbolsDetail.length == linkedSymbols.count) {
      state.hasReachedMax = true;
    } else {
      state.currentPage = state.currentPage + 1;
    }
    return TaskEither.of(linkedSymbols);
  }

  void _handleError(Exception exception, Emitter<SymbolsState> emit) {
    addError(exception);
    emit(state.copyWith(currentState: SymbolsProcessState.error()));
  }

  void _handleSuccess(
    Stream<SymbolQuoteModel> symbolPriceResult,
    Emitter<SymbolsState> emit,
  ) {
    addSubscription(
      symbolPriceResult.listen(
        (result) {
          add(SymbolsEvent.processSymbolPriceResult(result: result));
        },
        onError: (Object e, StackTrace stackTrace) {
          addError(e, stackTrace);
        },
      ),
    );
    emit(state.copyWith(currentState: SymbolsProcessState.success()));
  }

  void _processSymbolPriceResult(
    _ProcessSymbolPriceResult event,
    Emitter<SymbolsState> emit,
  ) {
    final symbolQuoteModel = event.result;
    final existingSymbolDetail =
        state.symbolsDetail[symbolQuoteModel.platformName];
    if (existingSymbolDetail == null) return;
    final symbolQuote =
        state.symbolsQuote[symbolQuoteModel.platformName] ??
        SymbolQuoteViewModel(
          ask: symbolQuoteModel.ask,
          bid: symbolQuoteModel.bid,
          digits: symbolQuoteModel.digits,
          spread: symbolQuoteModel.spread,
          direction: symbolQuoteModel.direction,
          dailyChange: symbolQuoteModel.dailyRateChange,
          midPrice: symbolQuoteModel.midPrice,
        );

    if (state.symbolsQuote[symbolQuoteModel.platformName] != null) {
      symbolQuote
        ..ask = symbolQuoteModel.ask
        ..bid = symbolQuoteModel.bid
        ..digits = symbolQuoteModel.digits
        ..spread = symbolQuoteModel.spread
        ..direction = symbolQuoteModel.direction
        ..midPrice = symbolQuoteModel.midPrice
        ..dailyChange = symbolQuoteModel.dailyRateChange;
    }

    state.symbolsQuote[symbolQuoteModel.platformName] = symbolQuote;
    state.symbolQuoteViewModel[state.selectedCategoryID] = state.symbolsQuote;
    if (isClosed) return;
    emit(state.copyWith(currentState: SymbolsProcessState.priceSucces()));
  }

  FutureOr<void> _onGoToDetails(_OnGoToDetails event) {
    return _getAccountNumberUseCase().fold((left) => "", (number) {
      _navigation.navigateToProductDetail(
        symbolDetail: event.symbolDetail,
        accountNumber: number,
        tradeDirection: event.tradeDirection,
      );
    });
  }

  FutureOr<void> _onSortSymbols(
    _OnSortSymbols event,
    Emitter<SymbolsState> emit,
  ) async {
    await _symbolLocalDataUseCase.saveSortingOption(event.sortOrder);
    emit(
      state.copyWith(
        currentState: SymbolsProcessState.loading(),
        currentPage: 1,
        hasReachedMax: false,
        sortOrder: event.sortOrder,
        symbolDetailViewModel: {},
      ),
    );

    add(SymbolsEvent.onGetSymbols());
  }

  void _onPriceViewChange(
    _OnPriceViewChange event,
    Emitter<SymbolsState> emit,
  ) {
    // Only update the view type, no data reload needed
    emit(state.copyWith(priceInfoViewType: event.viewType));
  }

  @override
  Future<void> close() {
    _symbolViewSubscription?.cancel();
    return super.close();
  }
}

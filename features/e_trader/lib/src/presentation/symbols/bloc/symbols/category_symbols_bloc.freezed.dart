// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category_symbols_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SymbolsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsEvent()';
}


}

/// @nodoc
class $SymbolsEventCopyWith<$Res>  {
$SymbolsEventCopyWith(SymbolsEvent _, $Res Function(SymbolsEvent) __);
}


/// @nodoc


class _OnGetSymbols implements SymbolsEvent {
  const _OnGetSymbols({this.query});
  

 final  String? query;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGetSymbolsCopyWith<_OnGetSymbols> get copyWith => __$OnGetSymbolsCopyWithImpl<_OnGetSymbols>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetSymbols&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString() {
  return 'SymbolsEvent.onGetSymbols(query: $query)';
}


}

/// @nodoc
abstract mixin class _$OnGetSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnGetSymbolsCopyWith(_OnGetSymbols value, $Res Function(_OnGetSymbols) _then) = __$OnGetSymbolsCopyWithImpl;
@useResult
$Res call({
 String? query
});




}
/// @nodoc
class __$OnGetSymbolsCopyWithImpl<$Res>
    implements _$OnGetSymbolsCopyWith<$Res> {
  __$OnGetSymbolsCopyWithImpl(this._self, this._then);

  final _OnGetSymbols _self;
  final $Res Function(_OnGetSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = freezed,}) {
  return _then(_OnGetSymbols(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _ProcessSymbolPriceResult implements SymbolsEvent {
  const _ProcessSymbolPriceResult({required this.result});
  

 final  SymbolQuoteModel result;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessSymbolPriceResultCopyWith<_ProcessSymbolPriceResult> get copyWith => __$ProcessSymbolPriceResultCopyWithImpl<_ProcessSymbolPriceResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessSymbolPriceResult&&(identical(other.result, result) || other.result == result));
}


@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString() {
  return 'SymbolsEvent.processSymbolPriceResult(result: $result)';
}


}

/// @nodoc
abstract mixin class _$ProcessSymbolPriceResultCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$ProcessSymbolPriceResultCopyWith(_ProcessSymbolPriceResult value, $Res Function(_ProcessSymbolPriceResult) _then) = __$ProcessSymbolPriceResultCopyWithImpl;
@useResult
$Res call({
 SymbolQuoteModel result
});


$SymbolQuoteModelCopyWith<$Res> get result;

}
/// @nodoc
class __$ProcessSymbolPriceResultCopyWithImpl<$Res>
    implements _$ProcessSymbolPriceResultCopyWith<$Res> {
  __$ProcessSymbolPriceResultCopyWithImpl(this._self, this._then);

  final _ProcessSymbolPriceResult _self;
  final $Res Function(_ProcessSymbolPriceResult) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_ProcessSymbolPriceResult(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,
  ));
}

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get result {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

/// @nodoc


class _OnSortSymbols implements SymbolsEvent {
  const _OnSortSymbols({this.sortOrder = SortOrderOptions.defaultOption});
  

@JsonKey() final  SortOrder sortOrder;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSortSymbolsCopyWith<_OnSortSymbols> get copyWith => __$OnSortSymbolsCopyWithImpl<_OnSortSymbols>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSortSymbols&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder);

@override
String toString() {
  return 'SymbolsEvent.onSortSymbols(sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class _$OnSortSymbolsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnSortSymbolsCopyWith(_OnSortSymbols value, $Res Function(_OnSortSymbols) _then) = __$OnSortSymbolsCopyWithImpl;
@useResult
$Res call({
 SortOrder sortOrder
});




}
/// @nodoc
class __$OnSortSymbolsCopyWithImpl<$Res>
    implements _$OnSortSymbolsCopyWith<$Res> {
  __$OnSortSymbolsCopyWithImpl(this._self, this._then);

  final _OnSortSymbols _self;
  final $Res Function(_OnSortSymbols) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sortOrder = null,}) {
  return _then(_OnSortSymbols(
sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,
  ));
}


}

/// @nodoc


class _OnPriceViewChange implements SymbolsEvent {
  const _OnPriceViewChange({this.viewType = SymbolPriceInfoViewTypeOptions.defaultOption});
  

@JsonKey() final  SymbolPriceInfoViewType viewType;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnPriceViewChangeCopyWith<_OnPriceViewChange> get copyWith => __$OnPriceViewChangeCopyWithImpl<_OnPriceViewChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnPriceViewChange&&(identical(other.viewType, viewType) || other.viewType == viewType));
}


@override
int get hashCode => Object.hash(runtimeType,viewType);

@override
String toString() {
  return 'SymbolsEvent.onPriceViewChange(viewType: $viewType)';
}


}

/// @nodoc
abstract mixin class _$OnPriceViewChangeCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnPriceViewChangeCopyWith(_OnPriceViewChange value, $Res Function(_OnPriceViewChange) _then) = __$OnPriceViewChangeCopyWithImpl;
@useResult
$Res call({
 SymbolPriceInfoViewType viewType
});




}
/// @nodoc
class __$OnPriceViewChangeCopyWithImpl<$Res>
    implements _$OnPriceViewChangeCopyWith<$Res> {
  __$OnPriceViewChangeCopyWithImpl(this._self, this._then);

  final _OnPriceViewChange _self;
  final $Res Function(_OnPriceViewChange) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? viewType = null,}) {
  return _then(_OnPriceViewChange(
viewType: null == viewType ? _self.viewType : viewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,
  ));
}


}

/// @nodoc


class _OnCategorySelected implements SymbolsEvent {
  const _OnCategorySelected({required this.categoryID});
  

 final  String categoryID;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnCategorySelectedCopyWith<_OnCategorySelected> get copyWith => __$OnCategorySelectedCopyWithImpl<_OnCategorySelected>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCategorySelected&&(identical(other.categoryID, categoryID) || other.categoryID == categoryID));
}


@override
int get hashCode => Object.hash(runtimeType,categoryID);

@override
String toString() {
  return 'SymbolsEvent.onCategorySelected(categoryID: $categoryID)';
}


}

/// @nodoc
abstract mixin class _$OnCategorySelectedCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnCategorySelectedCopyWith(_OnCategorySelected value, $Res Function(_OnCategorySelected) _then) = __$OnCategorySelectedCopyWithImpl;
@useResult
$Res call({
 String categoryID
});




}
/// @nodoc
class __$OnCategorySelectedCopyWithImpl<$Res>
    implements _$OnCategorySelectedCopyWith<$Res> {
  __$OnCategorySelectedCopyWithImpl(this._self, this._then);

  final _OnCategorySelected _self;
  final $Res Function(_OnCategorySelected) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? categoryID = null,}) {
  return _then(_OnCategorySelected(
categoryID: null == categoryID ? _self.categoryID : categoryID // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnGoToDetails implements SymbolsEvent {
  const _OnGoToDetails({required this.symbolDetail, this.tradeDirection});
  

 final  SymbolDetailViewModel symbolDetail;
 final  TradeType? tradeDirection;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGoToDetailsCopyWith<_OnGoToDetails> get copyWith => __$OnGoToDetailsCopyWithImpl<_OnGoToDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGoToDetails&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail)&&(identical(other.tradeDirection, tradeDirection) || other.tradeDirection == tradeDirection));
}


@override
int get hashCode => Object.hash(runtimeType,symbolDetail,tradeDirection);

@override
String toString() {
  return 'SymbolsEvent.gotoDetails(symbolDetail: $symbolDetail, tradeDirection: $tradeDirection)';
}


}

/// @nodoc
abstract mixin class _$OnGoToDetailsCopyWith<$Res> implements $SymbolsEventCopyWith<$Res> {
  factory _$OnGoToDetailsCopyWith(_OnGoToDetails value, $Res Function(_OnGoToDetails) _then) = __$OnGoToDetailsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel symbolDetail, TradeType? tradeDirection
});


$SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class __$OnGoToDetailsCopyWithImpl<$Res>
    implements _$OnGoToDetailsCopyWith<$Res> {
  __$OnGoToDetailsCopyWithImpl(this._self, this._then);

  final _OnGoToDetails _self;
  final $Res Function(_OnGoToDetails) _then;

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolDetail = null,Object? tradeDirection = freezed,}) {
  return _then(_OnGoToDetails(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,tradeDirection: freezed == tradeDirection ? _self.tradeDirection : tradeDirection // ignore: cast_nullable_to_non_nullable
as TradeType?,
  ));
}

/// Create a copy of SymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

/// @nodoc
mixin _$SymbolsState {

 Map<String, Map<String, SymbolDetailViewModel>> get symbolDetailViewModel; Map<String, Map<String, SymbolQuoteViewModel>> get symbolQuoteViewModel; SortOrder get sortOrder; set sortOrder(SortOrder value); SymbolPriceInfoViewType get priceInfoViewType; set priceInfoViewType(SymbolPriceInfoViewType value); String get selectedCategoryID; set selectedCategoryID(String value); int get currentPage; set currentPage(int value); bool get hasReachedMax; set hasReachedMax(bool value); int get symbolsCount; set symbolsCount(int value); SymbolsProcessState get currentState; set currentState(SymbolsProcessState value);
/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SymbolsStateCopyWith<SymbolsState> get copyWith => _$SymbolsStateCopyWithImpl<SymbolsState>(this as SymbolsState, _$identity);





@override
String toString() {
  return 'SymbolsState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $SymbolsStateCopyWith<$Res>  {
  factory $SymbolsStateCopyWith(SymbolsState value, $Res Function(SymbolsState) _then) = _$SymbolsStateCopyWithImpl;
@useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, SymbolsProcessState currentState
});


$SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$SymbolsStateCopyWithImpl<$Res>
    implements $SymbolsStateCopyWith<$Res> {
  _$SymbolsStateCopyWithImpl(this._self, this._then);

  final SymbolsState _self;
  final $Res Function(SymbolsState) _then;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,
  ));
}
/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _SymbolsState extends SymbolsState {
   _SymbolsState({required this.symbolDetailViewModel, required this.symbolQuoteViewModel, this.sortOrder = SortOrderOptions.defaultOption, this.priceInfoViewType = SymbolPriceInfoViewTypeOptions.defaultOption, this.selectedCategoryID = '', this.currentPage = 1, this.hasReachedMax = false, this.symbolsCount = 0, this.currentState = const SymbolsProcessState.loading()}): super._();
  

@override final  Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel;
@override final  Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel;
@override@JsonKey()  SortOrder sortOrder;
@override@JsonKey()  SymbolPriceInfoViewType priceInfoViewType;
@override@JsonKey()  String selectedCategoryID;
@override@JsonKey()  int currentPage;
@override@JsonKey()  bool hasReachedMax;
@override@JsonKey()  int symbolsCount;
@override@JsonKey()  SymbolsProcessState currentState;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SymbolsStateCopyWith<_SymbolsState> get copyWith => __$SymbolsStateCopyWithImpl<_SymbolsState>(this, _$identity);





@override
String toString() {
  return 'SymbolsState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$SymbolsStateCopyWith<$Res> implements $SymbolsStateCopyWith<$Res> {
  factory _$SymbolsStateCopyWith(_SymbolsState value, $Res Function(_SymbolsState) _then) = __$SymbolsStateCopyWithImpl;
@override @useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, SymbolsProcessState currentState
});


@override $SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$SymbolsStateCopyWithImpl<$Res>
    implements _$SymbolsStateCopyWith<$Res> {
  __$SymbolsStateCopyWithImpl(this._self, this._then);

  final _SymbolsState _self;
  final $Res Function(_SymbolsState) _then;

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? currentState = null,}) {
  return _then(_SymbolsState(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,
  ));
}

/// Create a copy of SymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$SymbolsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState()';
}


}

/// @nodoc
class $SymbolsProcessStateCopyWith<$Res>  {
$SymbolsProcessStateCopyWith(SymbolsProcessState _, $Res Function(SymbolsProcessState) __);
}


/// @nodoc


class SymbolsLoadingState implements SymbolsProcessState {
  const SymbolsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.loading()';
}


}




/// @nodoc


class SymbolschangeTabState implements SymbolsProcessState {
  const SymbolschangeTabState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolschangeTabState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.changeTab()';
}


}




/// @nodoc


class SymbolsSuccessState implements SymbolsProcessState {
  const SymbolsSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.success()';
}


}




/// @nodoc


class SymbolsErrorState implements SymbolsProcessState {
  const SymbolsErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.error()';
}


}




/// @nodoc


class SymbolsPriceSuccessState implements SymbolsProcessState {
  const SymbolsPriceSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsPriceSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.priceSucces()';
}


}




// dart format on

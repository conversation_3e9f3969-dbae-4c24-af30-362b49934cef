// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_symbol_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SearchSymbolEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SearchSymbolEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SearchSymbolEvent()';
}


}

/// @nodoc
class $SearchSymbolEventCopyWith<$Res>  {
$SearchSymbolEventCopyWith(SearchSymbolEvent _, $Res Function(SearchSymbolEvent) __);
}


/// @nodoc


class _OnSearchSymbols implements SearchSymbolEvent {
  const _OnSearchSymbols(this.query);
  

 final  String query;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSearchSymbolsCopyWith<_OnSearchSymbols> get copyWith => __$OnSearchSymbolsCopyWithImpl<_OnSearchSymbols>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSearchSymbols&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString() {
  return 'SearchSymbolEvent.onSearchSymbols(query: $query)';
}


}

/// @nodoc
abstract mixin class _$OnSearchSymbolsCopyWith<$Res> implements $SearchSymbolEventCopyWith<$Res> {
  factory _$OnSearchSymbolsCopyWith(_OnSearchSymbols value, $Res Function(_OnSearchSymbols) _then) = __$OnSearchSymbolsCopyWithImpl;
@useResult
$Res call({
 String query
});




}
/// @nodoc
class __$OnSearchSymbolsCopyWithImpl<$Res>
    implements _$OnSearchSymbolsCopyWith<$Res> {
  __$OnSearchSymbolsCopyWithImpl(this._self, this._then);

  final _OnSearchSymbols _self;
  final $Res Function(_OnSearchSymbols) _then;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = null,}) {
  return _then(_OnSearchSymbols(
null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _LoadMoreSymbols implements SearchSymbolEvent {
  const _LoadMoreSymbols();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadMoreSymbols);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SearchSymbolEvent.loadMoreSymbols()';
}


}




/// @nodoc


class _ProcessSymbolPriceResult implements SearchSymbolEvent {
  const _ProcessSymbolPriceResult({required this.result});
  

 final  SymbolQuoteModel result;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessSymbolPriceResultCopyWith<_ProcessSymbolPriceResult> get copyWith => __$ProcessSymbolPriceResultCopyWithImpl<_ProcessSymbolPriceResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessSymbolPriceResult&&(identical(other.result, result) || other.result == result));
}


@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString() {
  return 'SearchSymbolEvent.processSymbolPriceResult(result: $result)';
}


}

/// @nodoc
abstract mixin class _$ProcessSymbolPriceResultCopyWith<$Res> implements $SearchSymbolEventCopyWith<$Res> {
  factory _$ProcessSymbolPriceResultCopyWith(_ProcessSymbolPriceResult value, $Res Function(_ProcessSymbolPriceResult) _then) = __$ProcessSymbolPriceResultCopyWithImpl;
@useResult
$Res call({
 SymbolQuoteModel result
});


$SymbolQuoteModelCopyWith<$Res> get result;

}
/// @nodoc
class __$ProcessSymbolPriceResultCopyWithImpl<$Res>
    implements _$ProcessSymbolPriceResultCopyWith<$Res> {
  __$ProcessSymbolPriceResultCopyWithImpl(this._self, this._then);

  final _ProcessSymbolPriceResult _self;
  final $Res Function(_ProcessSymbolPriceResult) _then;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_ProcessSymbolPriceResult(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,
  ));
}

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get result {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

/// @nodoc


class _OnGoToDetails implements SearchSymbolEvent {
  const _OnGoToDetails({required this.symbolDetail, this.tradeDirection});
  

 final  SymbolDetailViewModel symbolDetail;
 final  TradeType? tradeDirection;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGoToDetailsCopyWith<_OnGoToDetails> get copyWith => __$OnGoToDetailsCopyWithImpl<_OnGoToDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGoToDetails&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail)&&(identical(other.tradeDirection, tradeDirection) || other.tradeDirection == tradeDirection));
}


@override
int get hashCode => Object.hash(runtimeType,symbolDetail,tradeDirection);

@override
String toString() {
  return 'SearchSymbolEvent.gotoDetails(symbolDetail: $symbolDetail, tradeDirection: $tradeDirection)';
}


}

/// @nodoc
abstract mixin class _$OnGoToDetailsCopyWith<$Res> implements $SearchSymbolEventCopyWith<$Res> {
  factory _$OnGoToDetailsCopyWith(_OnGoToDetails value, $Res Function(_OnGoToDetails) _then) = __$OnGoToDetailsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel symbolDetail, TradeType? tradeDirection
});


$SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class __$OnGoToDetailsCopyWithImpl<$Res>
    implements _$OnGoToDetailsCopyWith<$Res> {
  __$OnGoToDetailsCopyWithImpl(this._self, this._then);

  final _OnGoToDetails _self;
  final $Res Function(_OnGoToDetails) _then;

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolDetail = null,Object? tradeDirection = freezed,}) {
  return _then(_OnGoToDetails(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,tradeDirection: freezed == tradeDirection ? _self.tradeDirection : tradeDirection // ignore: cast_nullable_to_non_nullable
as TradeType?,
  ));
}

/// Create a copy of SearchSymbolEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

/// @nodoc


class _ResetAndClose implements SearchSymbolEvent {
  const _ResetAndClose();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResetAndClose);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SearchSymbolEvent.resetAndClose()';
}


}




/// @nodoc
mixin _$SearchSymbolState {

 Map<String, Map<String, SymbolDetailViewModel>> get symbolDetailViewModel; Map<String, Map<String, SymbolQuoteViewModel>> get symbolQuoteViewModel; SortOrder get sortOrder; set sortOrder(SortOrder value); SymbolPriceInfoViewType get priceInfoViewType; set priceInfoViewType(SymbolPriceInfoViewType value); String get selectedCategoryID; set selectedCategoryID(String value); int get currentPage; set currentPage(int value); bool get hasReachedMax; set hasReachedMax(bool value); int get symbolsCount; set symbolsCount(int value); List<String> get previousSearches; set previousSearches(List<String> value); String get currentQuery; set currentQuery(String value); SymbolsProcessState get currentState; set currentState(SymbolsProcessState value); bool get shouldClose; set shouldClose(bool value); bool get isClosing; set isClosing(bool value);
/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SearchSymbolStateCopyWith<SearchSymbolState> get copyWith => _$SearchSymbolStateCopyWithImpl<SearchSymbolState>(this as SearchSymbolState, _$identity);





@override
String toString() {
  return 'SearchSymbolState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, previousSearches: $previousSearches, currentQuery: $currentQuery, currentState: $currentState, shouldClose: $shouldClose, isClosing: $isClosing)';
}


}

/// @nodoc
abstract mixin class $SearchSymbolStateCopyWith<$Res>  {
  factory $SearchSymbolStateCopyWith(SearchSymbolState value, $Res Function(SearchSymbolState) _then) = _$SearchSymbolStateCopyWithImpl;
@useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, List<String> previousSearches, String currentQuery, SymbolsProcessState currentState, bool shouldClose, bool isClosing
});


$SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$SearchSymbolStateCopyWithImpl<$Res>
    implements $SearchSymbolStateCopyWith<$Res> {
  _$SearchSymbolStateCopyWithImpl(this._self, this._then);

  final SearchSymbolState _self;
  final $Res Function(SearchSymbolState) _then;

/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? previousSearches = null,Object? currentQuery = null,Object? currentState = null,Object? shouldClose = null,Object? isClosing = null,}) {
  return _then(_self.copyWith(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,previousSearches: null == previousSearches ? _self.previousSearches : previousSearches // ignore: cast_nullable_to_non_nullable
as List<String>,currentQuery: null == currentQuery ? _self.currentQuery : currentQuery // ignore: cast_nullable_to_non_nullable
as String,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,shouldClose: null == shouldClose ? _self.shouldClose : shouldClose // ignore: cast_nullable_to_non_nullable
as bool,isClosing: null == isClosing ? _self.isClosing : isClosing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _SearchSymbolState extends SearchSymbolState {
   _SearchSymbolState({required this.symbolDetailViewModel, required this.symbolQuoteViewModel, this.sortOrder = SortOrderOptions.defaultOption, this.priceInfoViewType = SymbolPriceInfoViewTypeOptions.defaultOption, this.selectedCategoryID = '', this.currentPage = 1, this.hasReachedMax = false, this.symbolsCount = 0, this.previousSearches = const <String>[], this.currentQuery = '', this.currentState = const SymbolsProcessState.initial(), this.shouldClose = false, this.isClosing = false}): super._();
  

@override final  Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel;
@override final  Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel;
@override@JsonKey()  SortOrder sortOrder;
@override@JsonKey()  SymbolPriceInfoViewType priceInfoViewType;
@override@JsonKey()  String selectedCategoryID;
@override@JsonKey()  int currentPage;
@override@JsonKey()  bool hasReachedMax;
@override@JsonKey()  int symbolsCount;
@override@JsonKey()  List<String> previousSearches;
@override@JsonKey()  String currentQuery;
@override@JsonKey()  SymbolsProcessState currentState;
@override@JsonKey()  bool shouldClose;
@override@JsonKey()  bool isClosing;

/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchSymbolStateCopyWith<_SearchSymbolState> get copyWith => __$SearchSymbolStateCopyWithImpl<_SearchSymbolState>(this, _$identity);





@override
String toString() {
  return 'SearchSymbolState(symbolDetailViewModel: $symbolDetailViewModel, symbolQuoteViewModel: $symbolQuoteViewModel, sortOrder: $sortOrder, priceInfoViewType: $priceInfoViewType, selectedCategoryID: $selectedCategoryID, currentPage: $currentPage, hasReachedMax: $hasReachedMax, symbolsCount: $symbolsCount, previousSearches: $previousSearches, currentQuery: $currentQuery, currentState: $currentState, shouldClose: $shouldClose, isClosing: $isClosing)';
}


}

/// @nodoc
abstract mixin class _$SearchSymbolStateCopyWith<$Res> implements $SearchSymbolStateCopyWith<$Res> {
  factory _$SearchSymbolStateCopyWith(_SearchSymbolState value, $Res Function(_SearchSymbolState) _then) = __$SearchSymbolStateCopyWithImpl;
@override @useResult
$Res call({
 Map<String, Map<String, SymbolDetailViewModel>> symbolDetailViewModel, Map<String, Map<String, SymbolQuoteViewModel>> symbolQuoteViewModel, SortOrder sortOrder, SymbolPriceInfoViewType priceInfoViewType, String selectedCategoryID, int currentPage, bool hasReachedMax, int symbolsCount, List<String> previousSearches, String currentQuery, SymbolsProcessState currentState, bool shouldClose, bool isClosing
});


@override $SymbolsProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$SearchSymbolStateCopyWithImpl<$Res>
    implements _$SearchSymbolStateCopyWith<$Res> {
  __$SearchSymbolStateCopyWithImpl(this._self, this._then);

  final _SearchSymbolState _self;
  final $Res Function(_SearchSymbolState) _then;

/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolDetailViewModel = null,Object? symbolQuoteViewModel = null,Object? sortOrder = null,Object? priceInfoViewType = null,Object? selectedCategoryID = null,Object? currentPage = null,Object? hasReachedMax = null,Object? symbolsCount = null,Object? previousSearches = null,Object? currentQuery = null,Object? currentState = null,Object? shouldClose = null,Object? isClosing = null,}) {
  return _then(_SearchSymbolState(
symbolDetailViewModel: null == symbolDetailViewModel ? _self.symbolDetailViewModel : symbolDetailViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolDetailViewModel>>,symbolQuoteViewModel: null == symbolQuoteViewModel ? _self.symbolQuoteViewModel : symbolQuoteViewModel // ignore: cast_nullable_to_non_nullable
as Map<String, Map<String, SymbolQuoteViewModel>>,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,selectedCategoryID: null == selectedCategoryID ? _self.selectedCategoryID : selectedCategoryID // ignore: cast_nullable_to_non_nullable
as String,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,symbolsCount: null == symbolsCount ? _self.symbolsCount : symbolsCount // ignore: cast_nullable_to_non_nullable
as int,previousSearches: null == previousSearches ? _self.previousSearches : previousSearches // ignore: cast_nullable_to_non_nullable
as List<String>,currentQuery: null == currentQuery ? _self.currentQuery : currentQuery // ignore: cast_nullable_to_non_nullable
as String,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as SymbolsProcessState,shouldClose: null == shouldClose ? _self.shouldClose : shouldClose // ignore: cast_nullable_to_non_nullable
as bool,isClosing: null == isClosing ? _self.isClosing : isClosing // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of SearchSymbolState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolsProcessStateCopyWith<$Res> get currentState {
  
  return $SymbolsProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$SymbolsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState()';
}


}

/// @nodoc
class $SymbolsProcessStateCopyWith<$Res>  {
$SymbolsProcessStateCopyWith(SymbolsProcessState _, $Res Function(SymbolsProcessState) __);
}


/// @nodoc


class SymbolsInitialState implements SymbolsProcessState {
  const SymbolsInitialState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsInitialState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.initial()';
}


}




/// @nodoc


class SymbolsLoadingState implements SymbolsProcessState {
  const SymbolsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.loading()';
}


}




/// @nodoc


class SymbolsSuccessState implements SymbolsProcessState {
  const SymbolsSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.success()';
}


}




/// @nodoc


class SymbolsErrorState implements SymbolsProcessState {
  const SymbolsErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.error()';
}


}




/// @nodoc


class SymbolsPriceSuccessState implements SymbolsProcessState {
  const SymbolsPriceSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SymbolsPriceSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SymbolsProcessState.priceSucces()';
}


}




// dart format on

part of 'search_symbol_bloc.dart';

@freezed
sealed class SearchSymbolEvent with _$SearchSymbolEvent {
  const factory SearchSymbolEvent.onSearchSymbols(String query) =
      _OnSearchSymbols;
  const factory SearchSymbolEvent.loadMoreSymbols() = _LoadMoreSymbols;
  const factory SearchSymbolEvent.processSymbolPriceResult({
    required SymbolQuoteModel result,
  }) = _ProcessSymbolPriceResult;
  const factory SearchSymbolEvent.gotoDetails({
    required SymbolDetailViewModel symbolDetail,
    TradeType? tradeDirection,
  }) = _OnGoToDetails;
  const factory SearchSymbolEvent.resetAndClose() = _ResetAndClose;
}

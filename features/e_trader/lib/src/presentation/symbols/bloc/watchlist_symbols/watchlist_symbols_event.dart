part of 'watchlist_symbols_bloc.dart';

@freezed
sealed class WatchlistSymbolsEvent with _$WatchlistSymbolsEvent {
  const factory WatchlistSymbolsEvent.onGetWatchlistSymbols() =
      _OnGetWatchlistSymbols;

  const factory WatchlistSymbolsEvent.onPriceViewChange({
    required SymbolPriceInfoViewType viewType,
  }) = _OnPriceViewChange;

  const factory WatchlistSymbolsEvent.onSortSymbols({
    required SortOrder sortOrder,
  }) = _OnSortSymbols;

  const factory WatchlistSymbolsEvent.gotoDetails({
    required SymbolDetailViewModel symbolDetail,
    TradeType? tradeDirection,
  }) = _GotoDetails;

  const factory WatchlistSymbolsEvent.processSymbolPriceResult({
    required SymbolQuoteModel result,
  }) = _ProcessPriceResult;
}

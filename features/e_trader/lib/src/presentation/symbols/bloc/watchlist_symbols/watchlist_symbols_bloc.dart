import 'dart:async';

import 'package:e_trader/src/data/api/linked_symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/watchlist_model.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_watchlist_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/symbol_quote_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbol_view/symbol_view_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'watchlist_symbols_bloc.freezed.dart';
part 'watchlist_symbols_event.dart';
part 'watchlist_symbols_state.dart';

class WatchlistSymbolsBloc
    extends Bloc<WatchlistSymbolsEvent, WatchlistSymbolsState>
    with DisposableMixin {
  WatchlistSymbolsBloc(
    this._subscribeToSymbolsQuotesUseCase,
    this._getWatchlistDataUseCase,
    this._getAccountNumberUseCase,
    this._navigation,
    this._symbolViewCubit,
    this._logger,
  ) : super(
        WatchlistSymbolsState(
          symbolsDetail: {},
          symbolsQuote: {},
          currentState: WatchlistSymbolsLoadingState(),
          priceInfoViewType: _symbolViewCubit.state.viewType,
          sortOrder: _symbolViewCubit.state.sortOrder,
        ),
      ) {
    on<_OnGetWatchlistSymbols>(_onGetWatchlistSymbols);
    on<_OnPriceViewChange>(_onPriceViewChange);
    on<_OnSortSymbols>(_onSortSymbols);
    on<_ProcessPriceResult>(_processSymbolPriceResult);
    on<_GotoDetails>(_onGotoDetails);

    _symbolViewSubscription = _symbolViewCubit.stream.listen((symbolViewState) {
      if (symbolViewState.sortOrder != state.sortOrder) {
        add(
          WatchlistSymbolsEvent.onSortSymbols(
            sortOrder: symbolViewState.sortOrder,
          ),
        );
      }

      if (symbolViewState.viewType != state.priceInfoViewType) {
        add(
          WatchlistSymbolsEvent.onPriceViewChange(
            viewType: symbolViewState.viewType,
          ),
        );
      }
    });

    add(WatchlistSymbolsEvent.onGetWatchlistSymbols());
  }

  final GetWatchlistDataUseCase _getWatchlistDataUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final EquitiTraderNavigation _navigation;
  final SubscribeToListOfSymbolQuotesUseCase _subscribeToSymbolsQuotesUseCase;
  final SymbolViewCubit _symbolViewCubit;
  final LoggerBase _logger;

  StreamSubscription<SymbolViewState>? _symbolViewSubscription;

  Future<void> _onGetWatchlistSymbols(
    _OnGetWatchlistSymbols event,
    Emitter<WatchlistSymbolsState> emit,
  ) async {
    if (isClosed) return;

    if (state.symbolsDetail.isEmpty)
      emit(state.copyWith(currentState: WatchlistSymbolsLoadingState()));
    final result =
        await _getWatchlistDataUseCase()
            .map((watchListModels) {
              final symbolModels =
                  watchListModels.map((w) => w.toSymbolModel()).toList();
              final linkedSymbolModel = LinkedSymbolModel(
                symbols: symbolModels,
                count: symbolModels.length,
              );
              return linkedSymbolModel;
            })
            .chainFirst(_updateSymbolsDetail)
            .flatMap(
              (_) => _subscribeToSymbolsQuotesUseCase(
                subscriberId: '${WatchlistSymbolsBloc}_$hashCode',
              ),
            )
            .run();
    return result.fold(
      (exception) => _handleError(exception, emit),
      (symbolPriceResult) => _handleSuccess(symbolPriceResult, emit),
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _updateSymbolsDetail(
    LinkedSymbolModel linkedSymbols,
  ) {
    final symbols =
        linkedSymbols.symbols
            .where((symbol) => symbol.platformName != null)
            .toList();

    // Always create a fresh symbols map to ensure cleanup when API returns empty list
    _createFreshSymbolsMap(symbols);

    // Also clean up quotes for symbols that are no longer in the watchlist
    _cleanupRemovedSymbolsQuotes(symbols);

    return TaskEither.of(linkedSymbols);
  }

  void _createFreshSymbolsMap(List<SymbolModel> symbols) {
    state.sortOrder == SortOrder.ascending
        ? symbols.sort((a, b) => a.tickerName!.compareTo(b.tickerName!))
        : symbols.sort((b, a) => a.tickerName!.compareTo(b.tickerName!));

    final symbolsMap = <String, SymbolDetailViewModel>{
      for (final symbol in symbols)
        symbol.platformName!: SymbolDetailViewModel(
          symbolName: symbol.tickerName!,
          platformName: symbol.platformName!,
          imageURL: symbol.productLogoUrl,
          assetType: symbol.assetType,
          minLot: symbol.minLot,
          maxLot: symbol.maxLot,
          digit: symbol.digits ?? 5,
          isForex: symbol.isForex,
          lotsSteps: symbol.lotsSteps ?? 0,
        ),
    };
    state.symbolsDetail = symbolsMap;
  }

  void _cleanupRemovedSymbolsQuotes(List<SymbolModel> symbols) {
    // Get the set of current symbol platform names
    final currentSymbolNames =
        symbols
            .where((symbol) => symbol.platformName != null)
            .map((symbol) => symbol.platformName!)
            .toSet();

    // Remove quotes for symbols that are no longer in the watchlist
    final existingQuoteKeys = state.symbolsQuote.keys.toList();
    for (final symbolName in existingQuoteKeys) {
      if (!currentSymbolNames.contains(symbolName)) {
        state.symbolsQuote.remove(symbolName);
      }
    }
  }

  void _handleError(Exception exception, Emitter<WatchlistSymbolsState> emit) {
    addError(exception);
    emit(state.copyWith(currentState: WatchlistSymbolsCurrentState.error()));
  }

  void _handleSuccess(
    Stream<SymbolQuoteModel> symbolPriceResult,
    Emitter<WatchlistSymbolsState> emit,
  ) {
    addSubscription(
      symbolPriceResult.listen(
        (result) {
          if (isClosed) return;
          add(WatchlistSymbolsEvent.processSymbolPriceResult(result: result));
        },
        onError: (Object e, StackTrace stackTrace) {
          addError(e, stackTrace);
        },
      ),
    );
    emit(state.copyWith(currentState: WatchlistSymbolsCurrentState.success()));
  }

  void _processSymbolPriceResult(
    _ProcessPriceResult event,
    Emitter<WatchlistSymbolsState> emit,
  ) {
    final symbolQuoteModel = event.result;
    final existingSymbolDetail =
        state.symbolsDetail[symbolQuoteModel.platformName];
    if (existingSymbolDetail == null) return;
    final symbolQuote =
        state.symbolsQuote[symbolQuoteModel.platformName] ??
        SymbolQuoteViewModel(
          ask: symbolQuoteModel.ask,
          bid: symbolQuoteModel.bid,
          digits: symbolQuoteModel.digits,
          spread: symbolQuoteModel.spread,
          direction: symbolQuoteModel.direction,
          dailyChange: symbolQuoteModel.dailyRateChange,
          midPrice: symbolQuoteModel.midPrice,
        );

    if (state.symbolsQuote[symbolQuoteModel.platformName] != null) {
      symbolQuote
        ..ask = symbolQuoteModel.ask
        ..bid = symbolQuoteModel.bid
        ..digits = symbolQuoteModel.digits
        ..spread = symbolQuoteModel.spread
        ..direction = symbolQuoteModel.direction
        ..midPrice = symbolQuoteModel.midPrice
        ..dailyChange = symbolQuoteModel.dailyRateChange;
    }

    state.symbolsQuote[symbolQuoteModel.platformName] = symbolQuote;
    if (isClosed) return;
    emit(
      state.copyWith(currentState: WatchlistSymbolsCurrentState.priceSuccess()),
    );
  }

  void _onSortSymbols(
    _OnSortSymbols event,
    Emitter<WatchlistSymbolsState> emit,
  ) {
    // Update sort order and rebuild the symbolsDetail map preserving insertion order
    if (state.symbolsDetail.isEmpty) {
      // No items yet; just update the sortOrder and exit without altering currentState
      emit(state.copyWith(sortOrder: event.sortOrder));
      return;
    }

    final items = state.symbolsDetail.values.toList();

    if (event.sortOrder == SortOrder.ascending) {
      items.sort((a, b) => a.symbolName.compareTo(b.symbolName));
    } else {
      items.sort((b, a) => a.symbolName.compareTo(b.symbolName));
    }

    final sortedMap = <String, SymbolDetailViewModel>{
      for (final s in items) s.platformName: s,
    };

    state.symbolsDetail = sortedMap;

    emit(state.copyWith(sortOrder: event.sortOrder));
  }

  void _onPriceViewChange(
    _OnPriceViewChange event,
    Emitter<WatchlistSymbolsState> emit,
  ) {
    emit(state.copyWith(priceInfoViewType: event.viewType));
  }

  void _onGotoDetails(_GotoDetails event, Emitter<WatchlistSymbolsState> emit) {
    final accountNumberResult = _getAccountNumberUseCase();

    accountNumberResult.fold(
      (error) {
        _logger.logError(
          'WatchlistSymbolsBloc: Failed to get account number for navigation: $error',
        );
      },
      (accountNumber) {
        _navigation.navigateToProductDetail(
          accountNumber: accountNumber,
          symbolDetail: event.symbolDetail,
          tradeDirection: event.tradeDirection,
        );
      },
    );
  }

  @override
  Future<void> close() {
    _symbolViewSubscription?.cancel();
    return super.close();
  }
}

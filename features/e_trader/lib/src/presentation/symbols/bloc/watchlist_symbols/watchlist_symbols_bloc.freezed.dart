// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'watchlist_symbols_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WatchlistSymbolsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsEvent()';
}


}

/// @nodoc
class $WatchlistSymbolsEventCopyWith<$Res>  {
$WatchlistSymbolsEventCopyWith(WatchlistSymbolsEvent _, $Res Function(WatchlistSymbolsEvent) __);
}


/// @nodoc


class _OnGetWatchlistSymbols implements WatchlistSymbolsEvent {
  const _OnGetWatchlistSymbols();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetWatchlistSymbols);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsEvent.onGetWatchlistSymbols()';
}


}




/// @nodoc


class _OnPriceViewChange implements WatchlistSymbolsEvent {
  const _OnPriceViewChange({required this.viewType});
  

 final  SymbolPriceInfoViewType viewType;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnPriceViewChangeCopyWith<_OnPriceViewChange> get copyWith => __$OnPriceViewChangeCopyWithImpl<_OnPriceViewChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnPriceViewChange&&(identical(other.viewType, viewType) || other.viewType == viewType));
}


@override
int get hashCode => Object.hash(runtimeType,viewType);

@override
String toString() {
  return 'WatchlistSymbolsEvent.onPriceViewChange(viewType: $viewType)';
}


}

/// @nodoc
abstract mixin class _$OnPriceViewChangeCopyWith<$Res> implements $WatchlistSymbolsEventCopyWith<$Res> {
  factory _$OnPriceViewChangeCopyWith(_OnPriceViewChange value, $Res Function(_OnPriceViewChange) _then) = __$OnPriceViewChangeCopyWithImpl;
@useResult
$Res call({
 SymbolPriceInfoViewType viewType
});




}
/// @nodoc
class __$OnPriceViewChangeCopyWithImpl<$Res>
    implements _$OnPriceViewChangeCopyWith<$Res> {
  __$OnPriceViewChangeCopyWithImpl(this._self, this._then);

  final _OnPriceViewChange _self;
  final $Res Function(_OnPriceViewChange) _then;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? viewType = null,}) {
  return _then(_OnPriceViewChange(
viewType: null == viewType ? _self.viewType : viewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,
  ));
}


}

/// @nodoc


class _OnSortSymbols implements WatchlistSymbolsEvent {
  const _OnSortSymbols({required this.sortOrder});
  

 final  SortOrder sortOrder;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSortSymbolsCopyWith<_OnSortSymbols> get copyWith => __$OnSortSymbolsCopyWithImpl<_OnSortSymbols>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSortSymbols&&(identical(other.sortOrder, sortOrder) || other.sortOrder == sortOrder));
}


@override
int get hashCode => Object.hash(runtimeType,sortOrder);

@override
String toString() {
  return 'WatchlistSymbolsEvent.onSortSymbols(sortOrder: $sortOrder)';
}


}

/// @nodoc
abstract mixin class _$OnSortSymbolsCopyWith<$Res> implements $WatchlistSymbolsEventCopyWith<$Res> {
  factory _$OnSortSymbolsCopyWith(_OnSortSymbols value, $Res Function(_OnSortSymbols) _then) = __$OnSortSymbolsCopyWithImpl;
@useResult
$Res call({
 SortOrder sortOrder
});




}
/// @nodoc
class __$OnSortSymbolsCopyWithImpl<$Res>
    implements _$OnSortSymbolsCopyWith<$Res> {
  __$OnSortSymbolsCopyWithImpl(this._self, this._then);

  final _OnSortSymbols _self;
  final $Res Function(_OnSortSymbols) _then;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sortOrder = null,}) {
  return _then(_OnSortSymbols(
sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,
  ));
}


}

/// @nodoc


class _GotoDetails implements WatchlistSymbolsEvent {
  const _GotoDetails({required this.symbolDetail, this.tradeDirection});
  

 final  SymbolDetailViewModel symbolDetail;
 final  TradeType? tradeDirection;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GotoDetailsCopyWith<_GotoDetails> get copyWith => __$GotoDetailsCopyWithImpl<_GotoDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GotoDetails&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail)&&(identical(other.tradeDirection, tradeDirection) || other.tradeDirection == tradeDirection));
}


@override
int get hashCode => Object.hash(runtimeType,symbolDetail,tradeDirection);

@override
String toString() {
  return 'WatchlistSymbolsEvent.gotoDetails(symbolDetail: $symbolDetail, tradeDirection: $tradeDirection)';
}


}

/// @nodoc
abstract mixin class _$GotoDetailsCopyWith<$Res> implements $WatchlistSymbolsEventCopyWith<$Res> {
  factory _$GotoDetailsCopyWith(_GotoDetails value, $Res Function(_GotoDetails) _then) = __$GotoDetailsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel symbolDetail, TradeType? tradeDirection
});


$SymbolDetailViewModelCopyWith<$Res> get symbolDetail;

}
/// @nodoc
class __$GotoDetailsCopyWithImpl<$Res>
    implements _$GotoDetailsCopyWith<$Res> {
  __$GotoDetailsCopyWithImpl(this._self, this._then);

  final _GotoDetails _self;
  final $Res Function(_GotoDetails) _then;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolDetail = null,Object? tradeDirection = freezed,}) {
  return _then(_GotoDetails(
symbolDetail: null == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel,tradeDirection: freezed == tradeDirection ? _self.tradeDirection : tradeDirection // ignore: cast_nullable_to_non_nullable
as TradeType?,
  ));
}

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res> get symbolDetail {
  
  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

/// @nodoc


class _ProcessPriceResult implements WatchlistSymbolsEvent {
  const _ProcessPriceResult({required this.result});
  

 final  SymbolQuoteModel result;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessPriceResultCopyWith<_ProcessPriceResult> get copyWith => __$ProcessPriceResultCopyWithImpl<_ProcessPriceResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessPriceResult&&(identical(other.result, result) || other.result == result));
}


@override
int get hashCode => Object.hash(runtimeType,result);

@override
String toString() {
  return 'WatchlistSymbolsEvent.processSymbolPriceResult(result: $result)';
}


}

/// @nodoc
abstract mixin class _$ProcessPriceResultCopyWith<$Res> implements $WatchlistSymbolsEventCopyWith<$Res> {
  factory _$ProcessPriceResultCopyWith(_ProcessPriceResult value, $Res Function(_ProcessPriceResult) _then) = __$ProcessPriceResultCopyWithImpl;
@useResult
$Res call({
 SymbolQuoteModel result
});


$SymbolQuoteModelCopyWith<$Res> get result;

}
/// @nodoc
class __$ProcessPriceResultCopyWithImpl<$Res>
    implements _$ProcessPriceResultCopyWith<$Res> {
  __$ProcessPriceResultCopyWithImpl(this._self, this._then);

  final _ProcessPriceResult _self;
  final $Res Function(_ProcessPriceResult) _then;

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_ProcessPriceResult(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,
  ));
}

/// Create a copy of WatchlistSymbolsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get result {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.result, (value) {
    return _then(_self.copyWith(result: value));
  });
}
}

/// @nodoc
mixin _$WatchlistSymbolsState {

 Map<String, SymbolDetailViewModel> get symbolsDetail; set symbolsDetail(Map<String, SymbolDetailViewModel> value); Map<String, SymbolQuoteViewModel> get symbolsQuote; set symbolsQuote(Map<String, SymbolQuoteViewModel> value); SymbolPriceInfoViewType get priceInfoViewType; set priceInfoViewType(SymbolPriceInfoViewType value); SortOrder get sortOrder; set sortOrder(SortOrder value); bool get hasReachedMax; set hasReachedMax(bool value); WatchlistSymbolsCurrentState get currentState; set currentState(WatchlistSymbolsCurrentState value);
/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WatchlistSymbolsStateCopyWith<WatchlistSymbolsState> get copyWith => _$WatchlistSymbolsStateCopyWithImpl<WatchlistSymbolsState>(this as WatchlistSymbolsState, _$identity);





@override
String toString() {
  return 'WatchlistSymbolsState(symbolsDetail: $symbolsDetail, symbolsQuote: $symbolsQuote, priceInfoViewType: $priceInfoViewType, sortOrder: $sortOrder, hasReachedMax: $hasReachedMax, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $WatchlistSymbolsStateCopyWith<$Res>  {
  factory $WatchlistSymbolsStateCopyWith(WatchlistSymbolsState value, $Res Function(WatchlistSymbolsState) _then) = _$WatchlistSymbolsStateCopyWithImpl;
@useResult
$Res call({
 Map<String, SymbolDetailViewModel> symbolsDetail, Map<String, SymbolQuoteViewModel> symbolsQuote, SymbolPriceInfoViewType priceInfoViewType, SortOrder sortOrder, bool hasReachedMax, WatchlistSymbolsCurrentState currentState
});


$WatchlistSymbolsCurrentStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$WatchlistSymbolsStateCopyWithImpl<$Res>
    implements $WatchlistSymbolsStateCopyWith<$Res> {
  _$WatchlistSymbolsStateCopyWithImpl(this._self, this._then);

  final WatchlistSymbolsState _self;
  final $Res Function(WatchlistSymbolsState) _then;

/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolsDetail = null,Object? symbolsQuote = null,Object? priceInfoViewType = null,Object? sortOrder = null,Object? hasReachedMax = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
symbolsDetail: null == symbolsDetail ? _self.symbolsDetail : symbolsDetail // ignore: cast_nullable_to_non_nullable
as Map<String, SymbolDetailViewModel>,symbolsQuote: null == symbolsQuote ? _self.symbolsQuote : symbolsQuote // ignore: cast_nullable_to_non_nullable
as Map<String, SymbolQuoteViewModel>,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as WatchlistSymbolsCurrentState,
  ));
}
/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WatchlistSymbolsCurrentStateCopyWith<$Res> get currentState {
  
  return $WatchlistSymbolsCurrentStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _WatchlistSymbolsState extends WatchlistSymbolsState {
   _WatchlistSymbolsState({required this.symbolsDetail, required this.symbolsQuote, this.priceInfoViewType = SymbolPriceInfoViewTypeOptions.defaultOption, this.sortOrder = SortOrderOptions.defaultOption, this.hasReachedMax = false, this.currentState = const WatchlistSymbolsCurrentState.loading()}): super._();
  

@override  Map<String, SymbolDetailViewModel> symbolsDetail;
@override  Map<String, SymbolQuoteViewModel> symbolsQuote;
@override@JsonKey()  SymbolPriceInfoViewType priceInfoViewType;
@override@JsonKey()  SortOrder sortOrder;
@override@JsonKey()  bool hasReachedMax;
@override@JsonKey()  WatchlistSymbolsCurrentState currentState;

/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WatchlistSymbolsStateCopyWith<_WatchlistSymbolsState> get copyWith => __$WatchlistSymbolsStateCopyWithImpl<_WatchlistSymbolsState>(this, _$identity);





@override
String toString() {
  return 'WatchlistSymbolsState(symbolsDetail: $symbolsDetail, symbolsQuote: $symbolsQuote, priceInfoViewType: $priceInfoViewType, sortOrder: $sortOrder, hasReachedMax: $hasReachedMax, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$WatchlistSymbolsStateCopyWith<$Res> implements $WatchlistSymbolsStateCopyWith<$Res> {
  factory _$WatchlistSymbolsStateCopyWith(_WatchlistSymbolsState value, $Res Function(_WatchlistSymbolsState) _then) = __$WatchlistSymbolsStateCopyWithImpl;
@override @useResult
$Res call({
 Map<String, SymbolDetailViewModel> symbolsDetail, Map<String, SymbolQuoteViewModel> symbolsQuote, SymbolPriceInfoViewType priceInfoViewType, SortOrder sortOrder, bool hasReachedMax, WatchlistSymbolsCurrentState currentState
});


@override $WatchlistSymbolsCurrentStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$WatchlistSymbolsStateCopyWithImpl<$Res>
    implements _$WatchlistSymbolsStateCopyWith<$Res> {
  __$WatchlistSymbolsStateCopyWithImpl(this._self, this._then);

  final _WatchlistSymbolsState _self;
  final $Res Function(_WatchlistSymbolsState) _then;

/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolsDetail = null,Object? symbolsQuote = null,Object? priceInfoViewType = null,Object? sortOrder = null,Object? hasReachedMax = null,Object? currentState = null,}) {
  return _then(_WatchlistSymbolsState(
symbolsDetail: null == symbolsDetail ? _self.symbolsDetail : symbolsDetail // ignore: cast_nullable_to_non_nullable
as Map<String, SymbolDetailViewModel>,symbolsQuote: null == symbolsQuote ? _self.symbolsQuote : symbolsQuote // ignore: cast_nullable_to_non_nullable
as Map<String, SymbolQuoteViewModel>,priceInfoViewType: null == priceInfoViewType ? _self.priceInfoViewType : priceInfoViewType // ignore: cast_nullable_to_non_nullable
as SymbolPriceInfoViewType,sortOrder: null == sortOrder ? _self.sortOrder : sortOrder // ignore: cast_nullable_to_non_nullable
as SortOrder,hasReachedMax: null == hasReachedMax ? _self.hasReachedMax : hasReachedMax // ignore: cast_nullable_to_non_nullable
as bool,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as WatchlistSymbolsCurrentState,
  ));
}

/// Create a copy of WatchlistSymbolsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WatchlistSymbolsCurrentStateCopyWith<$Res> get currentState {
  
  return $WatchlistSymbolsCurrentStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$WatchlistSymbolsCurrentState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsCurrentState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsCurrentState()';
}


}

/// @nodoc
class $WatchlistSymbolsCurrentStateCopyWith<$Res>  {
$WatchlistSymbolsCurrentStateCopyWith(WatchlistSymbolsCurrentState _, $Res Function(WatchlistSymbolsCurrentState) __);
}


/// @nodoc


class WatchlistSymbolsLoadingState implements WatchlistSymbolsCurrentState {
  const WatchlistSymbolsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsCurrentState.loading()';
}


}




/// @nodoc


class WatchlistSymbolsSuccessState implements WatchlistSymbolsCurrentState {
  const WatchlistSymbolsSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsCurrentState.success()';
}


}




/// @nodoc


class WatchlistSymbolsErrorState implements WatchlistSymbolsCurrentState {
  const WatchlistSymbolsErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsCurrentState.error()';
}


}




/// @nodoc


class WatchlistSymbolsPriceSuccessState implements WatchlistSymbolsCurrentState {
  const WatchlistSymbolsPriceSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WatchlistSymbolsPriceSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WatchlistSymbolsCurrentState.priceSuccess()';
}


}




// dart format on

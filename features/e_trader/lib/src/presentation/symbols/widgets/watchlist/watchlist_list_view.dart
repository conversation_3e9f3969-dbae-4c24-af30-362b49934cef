import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/watchlist_symbols/watchlist_symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/services/symbol_subscription_batch_service.dart';
import 'package:e_trader/src/presentation/symbols/widgets/search/empty_or_error_symbols_view.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:e_trader/src/presentation/symbols/widgets/watchlist/watchlist_list_item.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class WatchlistListView extends StatefulWidget {
  const WatchlistListView({
    super.key,
    this.emptyBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.query,
    this.isSearchView = false,
    required this.tabController,
    required this.tabIndex,
    required this.tabSubscriptionManager,
  });
  final WidgetBuilder? emptyBuilder;
  final WidgetBuilder? errorBuilder;
  final WidgetBuilder? loadingBuilder;
  final String? query;
  final bool isSearchView;
  final TabController tabController;
  final int tabIndex;
  final TabSubscriptionManager tabSubscriptionManager;

  @override
  State<WatchlistListView> createState() => _WatchlistListViewState();
}

class _WatchlistListViewState extends State<WatchlistListView>
    with AutomaticKeepAliveClientMixin, RouteAwareAppLifecycleMixin {
  List<String> _currentVisibleSymbols = [];
  late ListVisibilityController<String> _visibilityController;

  @override
  void initState() {
    super.initState();
    _visibilityController = ListVisibilityController();
    _initializeTabSubscriptionManager();
  }

  String _getSymbolAtIndex(int index) {
    final state = context.read<WatchlistSymbolsBloc>().state;
    final symbolEntry = state.symbolsDetail.entries.elementAtOrNull(index);
    if (symbolEntry != null) {
      return symbolEntry.value.platformName;
    }
    return ''; // Fallback for invalid index
  }

  void _initializeTabSubscriptionManager() {
    // Register with TabSubscriptionManager for proper tab visibility handling
    widget.tabSubscriptionManager.registerTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _onTabSubscribe,
      onUnsubscribe: _onTabUnsubscribe,
    );
  }

  void _onTabSubscribe() {
    diContainer<SymbolSubscriptionBatchService>().requestSubscriptions(
      _currentVisibleSymbols,
    );
  }

  void _onTabUnsubscribe() {
    diContainer<SymbolSubscriptionBatchService>().requestUnsubscriptions(
      _currentVisibleSymbols,
    );
  }

  void _handleVisibilityChanged(List<String> visibleSymbols) {
    if (_shouldUpdateSubscriptions()) {
      diContainer<SymbolSubscriptionBatchService>().updateSubscriptions(
        _currentVisibleSymbols, // previous
        visibleSymbols, // current
      );
    }

    // Update local state
    _currentVisibleSymbols = List.of(visibleSymbols);
  }

  bool _shouldUpdateSubscriptions() =>
      widget.tabSubscriptionManager.isActive &&
      widget.tabSubscriptionManager.currentActiveTab == widget.tabIndex;

  void _handleSymbolSelection(
    SymbolDetailViewModel symbolDetail,
    TradeType? tradeType,
  ) {
    // Remove the selected symbol from current visible symbols
    // This prevents it from being unsubscribed when the route is pushed
    final selectedSymbolPlatformName = symbolDetail.platformName;
    _currentVisibleSymbols =
        _currentVisibleSymbols
            .where((symbol) => symbol != selectedSymbolPlatformName)
            .toList();

    if (context.mounted) {
      context.read<WatchlistSymbolsBloc>().add(
        WatchlistSymbolsEvent.gotoDetails(
          symbolDetail: symbolDetail,
          tradeDirection: tradeType,
        ),
      );
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    if (_visibilityController.currentVisibleItems.isNotEmpty &&
        _shouldUpdateSubscriptions()) {
      _onTabSubscribe();
      if (!_currentVisibleSymbols.deepEquals(
        _visibilityController.currentVisibleItems,
      )) {
        _currentVisibleSymbols = List.of(
          _visibilityController.currentVisibleItems,
        );
      }
    }
    context.read<WatchlistSymbolsBloc>().add(
      WatchlistSymbolsEvent.onGetWatchlistSymbols(),
    );
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    if (_shouldUpdateSubscriptions()) {
      _onTabUnsubscribe();
    }
  }

  @override
  void onAppForeground() {
    super.onAppForeground();
    if (_shouldUpdateSubscriptions()) {
      _onTabSubscribe();
    }
  }

  @override
  void onAppBackground() {
    super.onAppBackground();
    if (_shouldUpdateSubscriptions()) {
      _onTabUnsubscribe();
    }
  }

  @override
  void dispose() {
    _visibilityController.clear();
    // Unregister from TabSubscriptionManager
    widget.tabSubscriptionManager.unregisterTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _onTabSubscribe,
      onUnsubscribe: _onTabUnsubscribe,
    );

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocListener(
      listeners: [
        // Error handling listener
        BlocListener<WatchlistSymbolsBloc, WatchlistSymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState &&
                  current.currentState is WatchlistSymbolsErrorState,
          listener: (listenerContext, state) {
            if (state.currentState is WatchlistSymbolsErrorState) {
              if (!Platform.environment.containsKey('FLUTTER_TEST')) {
                final toast = DuploToast();
                toast.hidesToastMessage();
                toast.showToastMessage(
                  autoCloseDuration: Duration.zero,
                  context: listenerContext,
                  widget: DuploToastMessage(
                    titleMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingError,
                    descriptionMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingErrorDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                    actionButtonTitle:
                        EquitiLocalization.of(listenerContext).trader_reload,
                    onTap: () {
                      toast.hidesToastMessage();
                      final watchlistBloc =
                          listenerContext.read<WatchlistSymbolsBloc>();
                      toast.hidesToastMessage();

                      // Reload watchlist data
                      watchlistBloc.add(
                        WatchlistSymbolsEvent.onGetWatchlistSymbols(),
                      );
                    },
                  ),
                );
              }
            }
          },
        ),
        BlocListener<WatchlistSymbolsBloc, WatchlistSymbolsState>(
          listenWhen:
              (previous, current) =>
                  previous.currentState != current.currentState &&
                  (current.currentState is WatchlistSymbolsSuccessState ||
                      current.currentState
                          is WatchlistSymbolsPriceSuccessState),
          listener: (listenerContext, state) {
            _visibilityController.updateItemCount(state.symbolsDetail.length);
          },
        ),
      ],
      child: ListVisibilityTracker<String>(
        itemHeight: kWatchlistSymbolItemHeightWithDivider,
        itemBuilder: _getSymbolAtIndex,
        onVisibilityChanged: _handleVisibilityChanged,
        controller: _visibilityController,
        child: CustomScrollView(
          slivers: [
            BlocBuilder<WatchlistSymbolsBloc, WatchlistSymbolsState>(
              buildWhen:
                  (previous, current) =>
                      previous.currentState != current.currentState ||
                      previous.sortOrder != current.sortOrder ||
                      previous.symbolsDetail.length !=
                          current.symbolsDetail.length,
              builder: (builderContext, state) {
                final isSuccess = switch (state.currentState) {
                  WatchlistSymbolsSuccessState() ||
                  WatchlistSymbolsPriceSuccessState() => true,
                  _ => false,
                };

                final symbolsDetail =
                    isSuccess
                        ? state.symbolsDetail
                        : <String, SymbolDetailViewModel>{};
                if (state.currentState is WatchlistSymbolsLoadingState) {
                  return SliverFillRemaining(
                    hasScrollBody: false,
                    child: DuploShimmerList(
                      hasLeading: true,
                      hasTrailing: true,
                    ),
                  );
                }
                if (state.currentState is WatchlistSymbolsErrorState) {
                  return SliverFillRemaining(
                    hasScrollBody: false,
                    child:
                        widget.errorBuilder?.call(context) ??
                        EmptyOrErrorSymbolsView(
                          message:
                              EquitiLocalization.of(
                                context,
                              ).trader_marketsLoadFailed,
                          title:
                              EquitiLocalization.of(
                                context,
                              ).trader_somethingWentWrong,
                          image: trader.Assets.images.searchError.svg(),
                        ),
                  );
                }

                if (isSuccess && symbolsDetail.isEmpty) {
                  return SliverFillRemaining(
                    hasScrollBody: false,
                    child:
                        widget.emptyBuilder?.call(context) ??
                        EmptyOrErrorSymbolsView(
                          message:
                              EquitiLocalization.of(
                                context,
                              ).trader_emptyWatchlistDescription,
                          title:
                              EquitiLocalization.of(
                                context,
                              ).trader_emptyWatchlist,
                          image: trader.Assets.images.emptyWatchlist.svg(),
                        ),
                  );
                }

                return SliverList.separated(
                  itemCount: symbolsDetail.length,
                  separatorBuilder:
                      (ctx, index) => Divider(
                        color: context.duploTheme.border.borderSecondary,
                        height: 1,
                      ),
                  itemBuilder: (ctx, index) {
                    final symbolDetailMap = symbolsDetail.entries
                        .elementAtOrNull(index);
                    return WatchlistListItem(
                      key: ValueKey(symbolDetailMap?.key),
                      symbol: symbolDetailMap!.value,
                      onTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            null,
                          ),
                      onBuyTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            TradeType.buy,
                          ),
                      onSellTap:
                          () => _handleSymbolSelection(
                            symbolDetailMap.value,
                            TradeType.sell,
                          ),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

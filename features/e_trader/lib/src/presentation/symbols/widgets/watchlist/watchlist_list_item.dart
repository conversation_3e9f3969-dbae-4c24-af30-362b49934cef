import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/symbols/widgets/symbol_info_widget.dart';
import 'package:e_trader/src/presentation/symbols/widgets/watchlist/watchlist_price_display.dart';
import 'package:flutter/material.dart';

/// Height of a single watchlist symbol item in pixels
const double kWatchlistSymbolItemHeight = 72.0;

/// Total height including divider (item height + 1px divider)
const double kWatchlistSymbolItemHeightWithDivider = 73.0;

class WatchlistListItem extends StatelessWidget {
  const WatchlistListItem({
    super.key,
    required this.symbol,
    this.onTap,
    this.onBuyTap,
    this.onSellTap,
  });

  final SymbolDetailViewModel symbol;
  final VoidCallback? onTap;
  final VoidCallback? onBuyTap;
  final VoidCallback? onSellTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: SizedBox(
          height: kWatchlistSymbolItemHeight,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(flex: 2, child: SymbolInfoWidget(symbol: symbol)),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 180, minHeight: 47),
                child: RepaintBoundary(
                  child: WatchlistPriceDisplay(
                    symbol: symbol.platformName,
                    key: ValueKey(symbol.platformName),
                    onBuyTap: onBuyTap,
                    onSellTap: onSellTap,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

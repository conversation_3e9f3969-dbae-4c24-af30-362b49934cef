import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/holiday_timing.dart';
import 'package:e_trader/src/data/api/market_session.dart';
import 'package:e_trader/src/domain/model/holiday_display_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class MarketTimingCalculator {
  final Map<WeekDay, List<MarketSession>> marketHours;
  final Map<WeekDay, List<HolidayTiming>> holidays;
  final Clock clock;

  // Time validation fields to detect manual time changes
  DateTime? _lastKnownTime;
  DateTime? _appStartTime;
  Duration _totalElapsedTime = Duration.zero;
  static const Duration _maxReasonableTimeJump = Duration(minutes: 5);
  static const Duration _maxReasonableBackwardJump = Duration(minutes: 1);

  MarketTimingCalculator({
    required this.clock,
    required this.marketHours,
    required this.holidays,
  }) {
    _appStartTime = clock.now();
    _lastKnownTime = _appStartTime;
  }

  bool _isTimeWithInRange(DateTime dateTime, DateTime start, DateTime end) {
    return dateTime.isAfter(start) && dateTime.isBefore(end);
  }

  /// Validates the current time and detects if the user has manually changed the device time.
  /// Returns a reliable time to use for calculations.
  DateTime _getValidatedTime() {
    final currentTime = clock.now();

    if (_lastKnownTime == null || _appStartTime == null) {
      _lastKnownTime = currentTime;
      return currentTime;
    }

    final timeDifference = currentTime.difference(_lastKnownTime!);
    final appRunTime = currentTime.difference(_appStartTime!);

    // Check for suspicious time jumps
    bool suspiciousTimeChange = false;

    // Forward jump that's too large (more than 5 minutes in a short period)
    if (timeDifference > _maxReasonableTimeJump &&
        appRunTime < Duration(hours: 1)) {
      suspiciousTimeChange = true;
      debugPrint(
        '⚠️ MarketTimingCalculator: Detected large forward time jump: ${timeDifference.inMinutes} minutes',
      );
    }

    // Backward time jump (time went backwards)
    if (timeDifference.isNegative &&
        timeDifference.abs() > _maxReasonableBackwardJump) {
      suspiciousTimeChange = true;
      debugPrint(
        '⚠️ MarketTimingCalculator: Detected backward time jump: ${timeDifference.inMinutes} minutes',
      );
    }

    // If time seems suspicious, use a more conservative approach
    if (suspiciousTimeChange) {
      debugPrint(
        '⚠️ MarketTimingCalculator: Using conservative time calculation due to suspected manual time change',
      );

      // Use the expected time based on app start time and elapsed duration
      final expectedTime = _appStartTime!.add(_totalElapsedTime);

      // Update our tracking with a reasonable progression
      _totalElapsedTime =
          _totalElapsedTime + Duration(seconds: 30); // Conservative increment
      _lastKnownTime = expectedTime;

      return expectedTime;
    }

    // Time seems reasonable, update our tracking
    _totalElapsedTime = appRunTime;
    _lastKnownTime = currentTime;

    return currentTime;
  }

  MarketStatus _marketStatusForDateTime(DateTime date) {
    DateTime dateUTC = date.toUtc();
    final day = dateUTC.toWeekDay;
    final marketTimes = marketHours[day] ?? [];

    for (final time in marketTimes) {
      DateTime marketStartTime = time.start.toUTCDate(dateUTC);
      DateTime marketEndTime = time.end.toUTCDate(dateUTC);

      if (_isTimeWithInRange(dateUTC, marketStartTime, marketEndTime)) {
        //Market is open given time.
        MarketStatus status = MarketStatus.open;
        //now check if there is any holiday this time.
        if (_isHoliday(dateUTC)) {
          status = MarketStatus.holiday;
        }

        return status;
      }
    }

    return MarketStatus.closed;
  }

  bool _isHoliday(DateTime time) {
    final day = time.toWeekDay;
    final holidayTimes = holidays[day] ?? [];
    for (final holiday in holidayTimes) {
      if (_isTimeWithInRange(time, holiday.dateTimeFrom, holiday.dateTimeTo)) {
        return true;
      }
    }
    return false;
  }

  DateTime? _marketTimeAfter(DateTime afterDate, bool isStartTime) {
    final afterUTCTime = afterDate.toUtc();
    // Start checking from the current day of the week
    int startIndex = afterUTCTime.toWeekDay!.number();
    DateTime loopStartDate = afterUTCTime;

    for (int i = 0; i < 7; i++) {
      final day = startIndex.toWeekDay!;
      final marketTimes = marketHours[day] ?? [];

      for (final time in marketTimes) {
        final mTime = isStartTime ? time.start : time.end;
        // Construct the market open/close DateTime for the current date
        final marketTime = mTime.toUTCDate(loopStartDate);

        // Market open time must be in future and not a holiday
        if (afterUTCTime.isBefore(marketTime)) {
          return marketTime; // Found the next open time
        }
      }

      // Move to the next day
      startIndex += 1;
      loopStartDate = loopStartDate.add(Duration(days: 1));
      if (startIndex > 7) {
        /// What if we started from Friday night and the next open.close time is on Monday morning.
        startIndex = 1;
      }
    }

    return null; // Return null if no open time is found
  }

  /// Determine if the market is open, closed, or on holiday
  MarketStatus currentMarketStatus() {
    final validatedTime = _getValidatedTime();
    return _marketStatusForDateTime(validatedTime);
  }

  String todayTimings() {
    final nowUTCTime = clock.now().toUtc();
    final day = nowUTCTime.toWeekDay;
    final marketTimes = marketHours[day] ?? [];
    String timings = "";
    for (final time in marketTimes) {
      if (timings.isNotEmpty) {
        timings += ', ';
      }
      DateTime start = time.start.toLocalDate(nowUTCTime);
      DateTime end = time.end.toLocalDate(nowUTCTime);
      timings +=
          '${start.hour.toTwoDigitString()}:${start.minute.toTwoDigitString()} - ${end.hour.toTwoDigitString()}:${end.minute.toTwoDigitString()}';
    }
    return timings;
  }

  DateTime? _nextOpenTimeAfter(DateTime time) {
    final utcTime = time.toUtc();
    int index = 0;
    DateTime? loopStartDate = utcTime;
    while (loopStartDate != null && index < 7) {
      final nextOpenTime = _marketTimeAfter(loopStartDate, true);
      if (nextOpenTime != null && !_isHoliday(nextOpenTime)) {
        return nextOpenTime;
      }
      loopStartDate = nextOpenTime;

      index++;
    }

    return null;
  }

  DateTime? nextOpenTime() {
    final validatedTime = _getValidatedTime();
    return _nextOpenTimeAfter(validatedTime);
  }

  DateTime? nextCloseTime() {
    final validatedTime = _getValidatedTime();
    final nowUTCTime = validatedTime.toUtc();
    final nextCloseTime = _marketTimeAfter(nowUTCTime, false);
    if (nextCloseTime != null) {
      /// Check if there is any Holiday before the next close time.Because in this case holiday startTime will be nextCloseTime.
      /// Also that hoildoay should be after the current time. And not already passed or started.
      final day = nextCloseTime.toWeekDay;
      final holidayTimes = holidays[day] ?? [];
      for (final hoiliday in holidayTimes) {
        if (hoiliday.dateTimeFrom.isBefore(nextCloseTime) &&
            nowUTCTime.isBefore(hoiliday.dateTimeFrom)) {
          return hoiliday.dateTimeFrom;
        }
      }
      return nextCloseTime;
    }

    return null;
  }

  List<KeyValuePair> marketHoursForDisplay(BuildContext context) {
    final loc = EquitiLocalization.of(context);
    DateFormat formatter = DateFormat('HH:mm');
    final List<KeyValuePair> returnMaps = <KeyValuePair>[];

    final nowTime = clock.now().toUtc();
    for (final weekDay in WeekDay.values) {
      final marketTimes = marketHours[weekDay] ?? [];
      String timings = "";
      for (final time in marketTimes) {
        if (timings.isNotEmpty) {
          timings += ', ';
        }

        DateTime marketStartTime = time.start.toLocalDate(nowTime);
        DateTime marketEndTime = time.end.toLocalDate(nowTime);
        timings +=
            '${formatter.format(marketStartTime)} - ${formatter.format(marketEndTime)}';
      }

      if (timings.isNotEmpty) {
        KeyValuePair pair = KeyValuePair(
          label: weekDay.localizedString(context),
          value: timings,
        );
        returnMaps.add(pair);
      } else {
        KeyValuePair pair = KeyValuePair(
          label: weekDay.localizedString(context),
          value: loc.trader_closed,
        );
        returnMaps.add(pair);
      }
    }

    return returnMaps;
  }

  List<HolidayDisplayModel> holidaysForDisplay(BuildContext _) {
    List<HolidayTiming> allHolidays = <HolidayTiming>[];
    List<HolidayDisplayModel> retDisplayModels = <HolidayDisplayModel>[];

    for (final weekDay in WeekDay.values) {
      final holidayTimes = holidays[weekDay] ?? [];
      allHolidays.addAll(holidayTimes);
    }

    final nowTime = clock.now().toUtc();
    allHolidays.sort((a, b) => a.dateTimeFrom.compareTo(b.dateTimeFrom));
    for (final holiday in allHolidays) {
      /// Don't show holidays that have already passed
      if (holiday.dateTimeTo.isAfter(nowTime)) {
        final localStart = holiday.dateTimeFrom.toLocal();
        final nextOpen = _nextOpenTimeAfter(holiday.dateTimeTo);

        HolidayDisplayModel model = HolidayDisplayModel(
          dateTimeStart: localStart,
          dateTimeNextOpen: nextOpen ?? holiday.dateTimeTo,
          title: holiday.description ?? '--',
        );

        retDisplayModels.add(model);
      }
    }

    return retDisplayModels;
  }

  Duration timeUntillNextStatusChange() {
    final validatedTime = _getValidatedTime();
    if (currentMarketStatus() == MarketStatus.open) {
      final nextClose = nextCloseTime();
      if (nextClose != null) {
        return nextClose.difference(validatedTime);
      }
    } else {
      final nextOpen = nextOpenTime();
      if (nextOpen != null) {
        return nextOpen.difference(validatedTime);
      }
    }

    return Duration.zero;
  }
}

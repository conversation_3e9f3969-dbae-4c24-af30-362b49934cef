// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'method_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MethodType implements DiagnosticableTreeMixin {

 Decimal get pipValue; Decimal get pipSize; Decimal get currentPrice; OrderLimitType get orderLimitType; TradeType get tradeType;
/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MethodTypeCopyWith<MethodType> get copyWith => _$MethodTypeCopyWithImpl<MethodType>(this as MethodType, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MethodType'))
    ..add(DiagnosticsProperty('pipValue', pipValue))..add(DiagnosticsProperty('pipSize', pipSize))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('orderLimitType', orderLimitType))..add(DiagnosticsProperty('tradeType', tradeType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MethodType&&(identical(other.pipValue, pipValue) || other.pipValue == pipValue)&&(identical(other.pipSize, pipSize) || other.pipSize == pipSize)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.orderLimitType, orderLimitType) || other.orderLimitType == orderLimitType)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType));
}


@override
int get hashCode => Object.hash(runtimeType,pipValue,pipSize,currentPrice,orderLimitType,tradeType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MethodType(pipValue: $pipValue, pipSize: $pipSize, currentPrice: $currentPrice, orderLimitType: $orderLimitType, tradeType: $tradeType)';
}


}

/// @nodoc
abstract mixin class $MethodTypeCopyWith<$Res>  {
  factory $MethodTypeCopyWith(MethodType value, $Res Function(MethodType) _then) = _$MethodTypeCopyWithImpl;
@useResult
$Res call({
 Decimal pipValue, Decimal pipSize, Decimal currentPrice, OrderLimitType orderLimitType, TradeType tradeType
});




}
/// @nodoc
class _$MethodTypeCopyWithImpl<$Res>
    implements $MethodTypeCopyWith<$Res> {
  _$MethodTypeCopyWithImpl(this._self, this._then);

  final MethodType _self;
  final $Res Function(MethodType) _then;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? pipValue = null,Object? pipSize = null,Object? currentPrice = null,Object? orderLimitType = null,Object? tradeType = null,}) {
  return _then(_self.copyWith(
pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as Decimal,pipSize: null == pipSize ? _self.pipSize : pipSize // ignore: cast_nullable_to_non_nullable
as Decimal,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,orderLimitType: null == orderLimitType ? _self.orderLimitType : orderLimitType // ignore: cast_nullable_to_non_nullable
as OrderLimitType,tradeType: null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,
  ));
}

}


/// @nodoc


class DistanceMethod with DiagnosticableTreeMixin implements MethodType {
  const DistanceMethod({required this.pipValue, required this.pipSize, required this.currentPrice, required this.orderLimitType, required this.tradeType, this.isModifyTrade});
  

@override final  Decimal pipValue;
@override final  Decimal pipSize;
@override final  Decimal currentPrice;
@override final  OrderLimitType orderLimitType;
@override final  TradeType tradeType;
 final  bool? isModifyTrade;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DistanceMethodCopyWith<DistanceMethod> get copyWith => _$DistanceMethodCopyWithImpl<DistanceMethod>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MethodType.distance'))
    ..add(DiagnosticsProperty('pipValue', pipValue))..add(DiagnosticsProperty('pipSize', pipSize))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('orderLimitType', orderLimitType))..add(DiagnosticsProperty('tradeType', tradeType))..add(DiagnosticsProperty('isModifyTrade', isModifyTrade));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DistanceMethod&&(identical(other.pipValue, pipValue) || other.pipValue == pipValue)&&(identical(other.pipSize, pipSize) || other.pipSize == pipSize)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.orderLimitType, orderLimitType) || other.orderLimitType == orderLimitType)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.isModifyTrade, isModifyTrade) || other.isModifyTrade == isModifyTrade));
}


@override
int get hashCode => Object.hash(runtimeType,pipValue,pipSize,currentPrice,orderLimitType,tradeType,isModifyTrade);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MethodType.distance(pipValue: $pipValue, pipSize: $pipSize, currentPrice: $currentPrice, orderLimitType: $orderLimitType, tradeType: $tradeType, isModifyTrade: $isModifyTrade)';
}


}

/// @nodoc
abstract mixin class $DistanceMethodCopyWith<$Res> implements $MethodTypeCopyWith<$Res> {
  factory $DistanceMethodCopyWith(DistanceMethod value, $Res Function(DistanceMethod) _then) = _$DistanceMethodCopyWithImpl;
@override @useResult
$Res call({
 Decimal pipValue, Decimal pipSize, Decimal currentPrice, OrderLimitType orderLimitType, TradeType tradeType, bool? isModifyTrade
});




}
/// @nodoc
class _$DistanceMethodCopyWithImpl<$Res>
    implements $DistanceMethodCopyWith<$Res> {
  _$DistanceMethodCopyWithImpl(this._self, this._then);

  final DistanceMethod _self;
  final $Res Function(DistanceMethod) _then;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pipValue = null,Object? pipSize = null,Object? currentPrice = null,Object? orderLimitType = null,Object? tradeType = null,Object? isModifyTrade = freezed,}) {
  return _then(DistanceMethod(
pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as Decimal,pipSize: null == pipSize ? _self.pipSize : pipSize // ignore: cast_nullable_to_non_nullable
as Decimal,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,orderLimitType: null == orderLimitType ? _self.orderLimitType : orderLimitType // ignore: cast_nullable_to_non_nullable
as OrderLimitType,tradeType: null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,isModifyTrade: freezed == isModifyTrade ? _self.isModifyTrade : isModifyTrade // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

/// @nodoc


class PriceMethod with DiagnosticableTreeMixin implements MethodType {
  const PriceMethod({required this.pipValue, required this.pipSize, required this.pipMultiplier, required this.currentPrice, required this.orderLimitType, required this.tradeType});
  

@override final  Decimal pipValue;
@override final  Decimal pipSize;
 final  Decimal pipMultiplier;
@override final  Decimal currentPrice;
@override final  OrderLimitType orderLimitType;
@override final  TradeType tradeType;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PriceMethodCopyWith<PriceMethod> get copyWith => _$PriceMethodCopyWithImpl<PriceMethod>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MethodType.price'))
    ..add(DiagnosticsProperty('pipValue', pipValue))..add(DiagnosticsProperty('pipSize', pipSize))..add(DiagnosticsProperty('pipMultiplier', pipMultiplier))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('orderLimitType', orderLimitType))..add(DiagnosticsProperty('tradeType', tradeType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriceMethod&&(identical(other.pipValue, pipValue) || other.pipValue == pipValue)&&(identical(other.pipSize, pipSize) || other.pipSize == pipSize)&&(identical(other.pipMultiplier, pipMultiplier) || other.pipMultiplier == pipMultiplier)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.orderLimitType, orderLimitType) || other.orderLimitType == orderLimitType)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType));
}


@override
int get hashCode => Object.hash(runtimeType,pipValue,pipSize,pipMultiplier,currentPrice,orderLimitType,tradeType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MethodType.price(pipValue: $pipValue, pipSize: $pipSize, pipMultiplier: $pipMultiplier, currentPrice: $currentPrice, orderLimitType: $orderLimitType, tradeType: $tradeType)';
}


}

/// @nodoc
abstract mixin class $PriceMethodCopyWith<$Res> implements $MethodTypeCopyWith<$Res> {
  factory $PriceMethodCopyWith(PriceMethod value, $Res Function(PriceMethod) _then) = _$PriceMethodCopyWithImpl;
@override @useResult
$Res call({
 Decimal pipValue, Decimal pipSize, Decimal pipMultiplier, Decimal currentPrice, OrderLimitType orderLimitType, TradeType tradeType
});




}
/// @nodoc
class _$PriceMethodCopyWithImpl<$Res>
    implements $PriceMethodCopyWith<$Res> {
  _$PriceMethodCopyWithImpl(this._self, this._then);

  final PriceMethod _self;
  final $Res Function(PriceMethod) _then;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pipValue = null,Object? pipSize = null,Object? pipMultiplier = null,Object? currentPrice = null,Object? orderLimitType = null,Object? tradeType = null,}) {
  return _then(PriceMethod(
pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as Decimal,pipSize: null == pipSize ? _self.pipSize : pipSize // ignore: cast_nullable_to_non_nullable
as Decimal,pipMultiplier: null == pipMultiplier ? _self.pipMultiplier : pipMultiplier // ignore: cast_nullable_to_non_nullable
as Decimal,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,orderLimitType: null == orderLimitType ? _self.orderLimitType : orderLimitType // ignore: cast_nullable_to_non_nullable
as OrderLimitType,tradeType: null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,
  ));
}


}

/// @nodoc


class ProfitOrLossMethod with DiagnosticableTreeMixin implements MethodType {
  const ProfitOrLossMethod({required this.pipValue, required this.pipSize, required this.currentPrice, required this.orderLimitType, required this.tradeType});
  

@override final  Decimal pipValue;
@override final  Decimal pipSize;
@override final  Decimal currentPrice;
@override final  OrderLimitType orderLimitType;
@override final  TradeType tradeType;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProfitOrLossMethodCopyWith<ProfitOrLossMethod> get copyWith => _$ProfitOrLossMethodCopyWithImpl<ProfitOrLossMethod>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MethodType.profitOrLoss'))
    ..add(DiagnosticsProperty('pipValue', pipValue))..add(DiagnosticsProperty('pipSize', pipSize))..add(DiagnosticsProperty('currentPrice', currentPrice))..add(DiagnosticsProperty('orderLimitType', orderLimitType))..add(DiagnosticsProperty('tradeType', tradeType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProfitOrLossMethod&&(identical(other.pipValue, pipValue) || other.pipValue == pipValue)&&(identical(other.pipSize, pipSize) || other.pipSize == pipSize)&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice)&&(identical(other.orderLimitType, orderLimitType) || other.orderLimitType == orderLimitType)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType));
}


@override
int get hashCode => Object.hash(runtimeType,pipValue,pipSize,currentPrice,orderLimitType,tradeType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MethodType.profitOrLoss(pipValue: $pipValue, pipSize: $pipSize, currentPrice: $currentPrice, orderLimitType: $orderLimitType, tradeType: $tradeType)';
}


}

/// @nodoc
abstract mixin class $ProfitOrLossMethodCopyWith<$Res> implements $MethodTypeCopyWith<$Res> {
  factory $ProfitOrLossMethodCopyWith(ProfitOrLossMethod value, $Res Function(ProfitOrLossMethod) _then) = _$ProfitOrLossMethodCopyWithImpl;
@override @useResult
$Res call({
 Decimal pipValue, Decimal pipSize, Decimal currentPrice, OrderLimitType orderLimitType, TradeType tradeType
});




}
/// @nodoc
class _$ProfitOrLossMethodCopyWithImpl<$Res>
    implements $ProfitOrLossMethodCopyWith<$Res> {
  _$ProfitOrLossMethodCopyWithImpl(this._self, this._then);

  final ProfitOrLossMethod _self;
  final $Res Function(ProfitOrLossMethod) _then;

/// Create a copy of MethodType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? pipValue = null,Object? pipSize = null,Object? currentPrice = null,Object? orderLimitType = null,Object? tradeType = null,}) {
  return _then(ProfitOrLossMethod(
pipValue: null == pipValue ? _self.pipValue : pipValue // ignore: cast_nullable_to_non_nullable
as Decimal,pipSize: null == pipSize ? _self.pipSize : pipSize // ignore: cast_nullable_to_non_nullable
as Decimal,currentPrice: null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as Decimal,orderLimitType: null == orderLimitType ? _self.orderLimitType : orderLimitType // ignore: cast_nullable_to_non_nullable
as OrderLimitType,tradeType: null == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType,
  ));
}


}

// dart format on

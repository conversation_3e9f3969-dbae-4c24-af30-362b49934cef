import 'package:api_client/api_client.dart';
import 'package:dio/dio.dart';
import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/close_trade_response_model.dart';
import 'package:e_trader/src/data/api/create_trade_model.dart';
import 'package:e_trader/src/data/api/create_trade_request_model.dart';
import 'package:e_trader/src/data/api/modify_trade_model.dart';
import 'package:e_trader/src/data/api/modify_trade_response_model.dart';
import 'package:e_trader/src/data/pool/position_model_pool.dart';
import 'package:e_trader/src/data/socket/position_response.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class PositionRepository {
  final ApiClientBase apiClient;
  SocketClient socketClient;
  final PositionModelPool positionModelPool;
  PositionRepository({
    required this.apiClient,
    required this.socketClient,
    required this.positionModelPool,
  });

  Exception _parseClientException(ClientException error) {
    final dioError = error.cause as DioException;
    final responseData = dioError.response?.data;

    if (responseData is Map<String, dynamic>) {
      return PositionsAndOrdersException.fromJson(responseData);
    }

    // If response is String or other type, return the original error
    return error;
  }

  TaskEither<Exception, CreateTradeModel> createPosition(
    CreateTradeRequestModel createTradeRequestModel,
  ) => apiClient
      .post<Map<String, dynamic>>(
        '/api/Position/open/v2',
        data: createTradeRequestModel.toJson(),
      )
      .mapLeft((error) {
        if (error is ClientException) {
          return _parseClientException(error);
        }
        return error;
      })
      .flatMap<CreateTradeModel>((response) {
        return TaskEither.tryCatch(
          () async => CreateTradeModel.fromJson(response.data!),
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });
  TaskEither<Exception, CloseTradeResponseModel?> closePosition(
    CloseTradeRequestModel closeTradeRequestModel,
  ) => apiClient
      .post<List<Object?>>(
        'api/Position/close',
        data: closeTradeRequestModel.toJson(),
      )
      .mapLeft((error) {
        if (error is ClientException) {
          return _parseClientException(error);
        }
        return error;
      })
      .flatMap<CloseTradeResponseModel?>(
        (response) => TaskEither.tryCatch(
          () async =>
              CloseTradeResponseModel.fromJsonList(response.data!).firstOrNull,
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        ),
      );
  TaskEither<Exception, ModifyTradeResponseModel> updatePosition(
    ModifyTradeModel modifyTradeModel,
  ) => apiClient
      .put<Map<String, dynamic>>(
        'api/Position/update',
        data: modifyTradeModel.toJson(),
      )
      .mapLeft((error) {
        if (error is ClientException) {
          return _parseClientException(error);
        }
        return error;
      })
      .flatMap<ModifyTradeResponseModel>((response) {
        return TaskEither.tryCatch(
          () async {
            return ModifyTradeResponseModel.fromJson(response.data!);
          },
          (error, stackTrace) {
            final errorMap = error as Map<String, dynamic>;
            return PositionsAndOrdersException.fromJson(errorMap);
          },
        );
      });
  TaskEither<Exception, Stream<PositionResponse?>>
  subscribeToPositionProductInfo({
    required String accountNumber,
    int positionId = 0,
    required EventType eventType,
    required String subscriberId,
    String symbolName = '',
  }) {
    return socketClient
        .subscribe(
          path: 'positionHub',
          eventType: eventType,
          targets: ["PositionUpdated", "PositionAdded", "PositionDeleted"],
          args: [
            {
              'accountNumber': accountNumber,
              'SymbolCode': symbolName,
              'IsWebRequest': false,
              'positionId': positionId,
            },
          ],
          subscriberId: subscriberId,
        )
        .flatMap((result) {
          return TaskEither.tryCatch(
            () async => result.map((data) {
              if (data == null) {
                return null;
              }
              final response = data as Map<String, dynamic>;
              final target = response['target'] as String?;
              final positionJson =
                  response['arguments'] as Map<String, dynamic>;

              if (positionId != 0 &&
                  int.parse(positionJson['positionId'] as String) !=
                      positionId) {
                return null;
              }

              final processedPosition = positionModelPool.processPosition(
                target,
                positionJson,
              );

              return PositionResponse(
                target: target,
                position: processedPosition,
              );
            }),
            (error, stackTrace) => Exception(error),
          );
        });
  }

  Future<void> updatePositionProductInfo({
    required String accountNumber,
    int positionId = 0,
    required EventType eventType,
    String symbolName = '',
  }) {
    if (eventType is SubscribeEvent) {
      positionModelPool.clear();
    }
    return socketClient.updateSubscription(
      path: 'positionHub',
      eventType: eventType,
      targets: ["PositionUpdated", "PositionAdded", "PositionDeleted"],
      args: [
        {
          'accountNumber': accountNumber,
          'SymbolCode': symbolName,
          'IsWebRequest': false,
          'positionId': positionId,
        },
      ],
    );
  }
}

import 'package:api_client/api_client.dart';
import 'package:prelude/prelude.dart';

/// Repository for fetching server time from reliable sources.
/// 
/// This repository provides multiple fallback options for getting accurate
/// server time, which is crucial for market hours calculations when users
/// might have manually changed their device time.
class ServerTimeRepository {
  final ApiClientBase _apiClient;
  
  const ServerTimeRepository({
    required ApiClientBase apiClient,
  }) : _apiClient = apiClient;

  /// Fetches server time from the primary API endpoint.
  /// 
  /// This method tries multiple approaches to get accurate server time:
  /// 1. First tries to get time from the main trading API
  /// 2. Falls back to world time API if main API fails
  /// 3. Uses HTTP Date header as last resort
  TaskEither<Exception, DateTime> getServerTime() {
    return _getTimeFromTradingAPI()
        .flatMap((time) => time != null 
            ? TaskEither.right(time)
            : _getTimeFromWorldTimeAPI())
        .flatMap((time) => time != null
            ? TaskEither.right(time)
            : _getTimeFromHttpHeaders());
  }

  /// Attempts to get server time from the main trading API.
  /// This is the preferred method as it uses the same server infrastructure.
  TaskEither<Exception, DateTime?> _getTimeFromTradingAPI() {
    return TaskEither.tryCatch(
      () async {
        try {
          // Try to get server time from a lightweight endpoint
          // We'll use the market sessions endpoint as it's likely to be fast
          final response = await _apiClient.get<Map<String, dynamic>>(
            'api/Symbol/get-session',
            queryParams: {
              'symbolCode': 'EURUSD', // Use a common symbol
              'accountNumber': '0', // Dummy account for time check
            },
            options: ApiRequestOptions(
              timeout: const Duration(seconds: 5),
              headers: {'X-Time-Request': 'true'},
            ),
          );

          // Extract time from response headers or body
          final serverTimeHeader = response.headers?['date'] ?? 
                                 response.headers?['Date'];
          
          if (serverTimeHeader != null) {
            return DateTime.tryParse(serverTimeHeader);
          }

          // If no header, check if response contains timestamp
          final data = response.data;
          if (data != null && data['serverTime'] != null) {
            return DateTime.tryParse(data['serverTime'].toString());
          }

          return null;
        } catch (e) {
          // Don't throw here, let it fall through to next method
          return null;
        }
      },
      (error, stackTrace) => Exception('Trading API time fetch failed: $error'),
    );
  }

  /// Attempts to get server time from World Time API as fallback.
  TaskEither<Exception, DateTime?> _getTimeFromWorldTimeAPI() {
    return TaskEither.tryCatch(
      () async {
        try {
          // Use a reliable world time API
          final response = await _apiClient.get<Map<String, dynamic>>(
            'http://worldtimeapi.org/api/timezone/UTC',
            options: ApiRequestOptions(
              timeout: const Duration(seconds: 5),
            ),
          );

          final data = response.data;
          if (data != null && data['utc_datetime'] != null) {
            return DateTime.tryParse(data['utc_datetime'].toString());
          }

          return null;
        } catch (e) {
          return null;
        }
      },
      (error, stackTrace) => Exception('World Time API fetch failed: $error'),
    );
  }

  /// Last resort: extract time from HTTP Date headers.
  TaskEither<Exception, DateTime> _getTimeFromHttpHeaders() {
    return TaskEither.tryCatch(
      () async {
        try {
          // Make a simple HEAD request to get server time from headers
          final response = await _apiClient.get<void>(
            'https://www.google.com',
            options: ApiRequestOptions(
              timeout: const Duration(seconds: 5),
            ),
          );

          final dateHeader = response.headers?['date'] ?? 
                           response.headers?['Date'];
          
          if (dateHeader != null) {
            final serverTime = DateTime.tryParse(dateHeader);
            if (serverTime != null) {
              return serverTime;
            }
          }

          // If all else fails, use current UTC time
          // This is not ideal but better than completely wrong time
          throw Exception('Could not extract time from HTTP headers');
        } catch (e) {
          throw Exception('HTTP header time extraction failed: $e');
        }
      },
      (error, stackTrace) => Exception('Server time fetch completely failed: $error'),
    );
  }

  /// Alternative method using a more reliable time service.
  /// This can be used if the primary method consistently fails.
  TaskEither<Exception, DateTime> getTimeFromTimeAPI() {
    return TaskEither.tryCatch(
      () async {
        final response = await _apiClient.get<Map<String, dynamic>>(
          'https://timeapi.io/api/Time/current/zone?timeZone=UTC',
          options: ApiRequestOptions(
            timeout: const Duration(seconds: 8),
          ),
        );

        final data = response.data;
        if (data != null && data['dateTime'] != null) {
          final timeString = data['dateTime'].toString();
          final serverTime = DateTime.tryParse(timeString);
          
          if (serverTime != null) {
            return serverTime;
          }
        }

        throw Exception('Invalid response from TimeAPI');
      },
      (error, stackTrace) => Exception('TimeAPI fetch failed: $error'),
    );
  }
}

import 'package:e_trader/src/data/pool/price_alert_pool.dart';
import 'package:e_trader/src/data/socket/active_alert_response.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class GetActiveAlertRepository {
  SocketClient socketClient;
  final PriceAlertPool alertPool;

  GetActiveAlertRepository({
    required this.socketClient,
    required this.alertPool,
  });

  TaskEither<Exception, Stream<ActiveAlertResponse?>> subscribeToAlerts({
    required String? symbol,
    required String accountNumber,
    required EventType eventType,
    required String subscriberId,
  }) {
    return socketClient
        .subscribe(
          path: 'priceAlertHub',
          eventType: eventType,
          subscriberId: subscriberId,
          targets: [
            "priceAlertUpdated",
            "priceAlertAdded",
            "priceAlertDeleted",
            "priceAlertTriggered",
          ],
          args: [
            {"symbolCode": symbol ?? "", "accountNumber": accountNumber},
          ],
        )
        .flatMap((result) {
          return TaskEither.tryCatch(
            () async => result.map((data) {
              if (data == null) {
                return null;
              }
              final response = data as Map<String, dynamic>;
              final target = response['target'] as String?;
              final alertJson = response['arguments'] as Map<String, dynamic>;

              // Use pool to process the alert
              final processedAlert = alertPool.processAlert(target, alertJson);

              // Return response with pooled alert
              return ActiveAlertResponse(target: target, alert: processedAlert);
            }),
            (error, stackTrace) => Exception(error),
          );
        });
  }

  Future<void> updateActiveAlerts({
    required String? symbol,
    required String accountNumber,
    required EventType eventType,
  }) {
    return socketClient.updateSubscription(
      path: 'priceAlertHub',
      eventType: eventType,
      targets: [
        "priceAlertUpdated",
        "priceAlertAdded",
        "priceAlertDeleted",
        "priceAlertTriggered",
      ],
      args:
          eventType is UnsubscribeEvent
              ? [accountNumber]
              : [
                {"symbolCode": symbol ?? "", "accountNumber": accountNumber},
              ],
    );
  }
}

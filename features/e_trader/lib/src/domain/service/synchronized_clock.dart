import 'package:clock/clock.dart';
import 'package:e_trader/src/domain/service/server_time_service.dart';

/// A Clock implementation that provides server-synchronized time.
/// 
/// This clock uses the ServerTimeService to provide accurate time that is
/// not affected by manual changes to the device's system time. This is
/// crucial for market hours calculations and other time-sensitive operations.
/// 
/// If server synchronization is not available or has failed, it gracefully
/// falls back to the device's local time.
class SynchronizedClock extends Clock {
  final ServerTimeService _serverTimeService;
  final Clock _fallbackClock;

  SynchronizedClock({
    required ServerTimeService serverTimeService,
    Clock? fallbackClock,
  }) : _serverTimeService = serverTimeService,
       _fallbackClock = fallbackClock ?? const Clock();

  @override
  DateTime now() {
    // Use server-synchronized time if available, otherwise fall back to device time
    return _serverTimeService.now;
  }

  /// Gets the current time offset from server time.
  /// Returns Duration.zero if synchronization is not available.
  Duration get timeOffset => _serverTimeService.timeOffset;

  /// Whether this clock is currently synchronized with server time.
  bool get isSynchronized => _serverTimeService.isInitialized;

  /// Gets the last time synchronization occurred.
  DateTime? get lastSyncTime => _serverTimeService.lastSyncTime;

  /// Manually triggers a time synchronization.
  /// Returns true if synchronization was successful.
  Future<bool> sync() => _serverTimeService.sync();

  /// Gets the raw device time without any server synchronization.
  DateTime get deviceTime => _fallbackClock.now();

  /// Gets a human-readable status of the clock synchronization.
  String get synchronizationStatus {
    if (!_serverTimeService.isInitialized) {
      return 'Not synchronized - using device time';
    }

    final lastSync = _serverTimeService.lastSyncTime;
    if (lastSync == null) {
      return 'Synchronized but no sync time available';
    }

    final timeSinceSync = DateTime.now().difference(lastSync);
    if (timeSinceSync.inMinutes < 1) {
      return 'Recently synchronized (${timeSinceSync.inSeconds}s ago)';
    } else if (timeSinceSync.inHours < 1) {
      return 'Synchronized ${timeSinceSync.inMinutes}m ago';
    } else {
      return 'Synchronized ${timeSinceSync.inHours}h ago';
    }
  }
}

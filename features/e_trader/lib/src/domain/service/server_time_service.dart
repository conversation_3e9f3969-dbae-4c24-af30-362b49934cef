import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:prelude/prelude.dart';

/// Service responsible for synchronizing with server time to handle cases
/// where users manually change their device time.
/// 
/// This service fetches server time from a reliable source and calculates
/// the offset between server time and device time, allowing the app to
/// provide accurate time-based calculations regardless of device time changes.
class ServerTimeService {
  static const Duration _syncInterval = Duration(minutes: 15);
  static const Duration _syncTimeout = Duration(seconds: 10);
  static const int _maxRetries = 3;
  
  final Future<TaskEither<Exception, DateTime>> Function() _fetchServerTime;
  
  DateTime? _lastSyncTime;
  Duration _timeOffset = Duration.zero;
  bool _isInitialized = false;
  Timer? _periodicSyncTimer;
  Completer<void>? _initializationCompleter;
  
  ServerTimeService({
    required Future<TaskEither<Exception, DateTime>> Function() fetchServerTime,
  }) : _fetchServerTime = fetchServerTime;

  /// Gets the current synchronized time.
  /// If synchronization hasn't occurred or failed, returns device time.
  DateTime get now {
    if (!_isInitialized) {
      debugPrint('⚠️ ServerTimeService: Not initialized, using device time');
      return DateTime.now();
    }
    
    final correctedTime = DateTime.now().add(_timeOffset);
    return correctedTime;
  }

  /// Gets the current time offset between server and device time.
  Duration get timeOffset => _timeOffset;

  /// Whether the service has been successfully initialized with server time.
  bool get isInitialized => _isInitialized;

  /// Gets the last time synchronization occurred.
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Initializes the service by performing initial time synchronization.
  /// This should be called once during app startup.
  Future<void> initialize() async {
    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    try {
      debugPrint('🕐 ServerTimeService: Initializing...');
      
      final success = await _performSync();
      if (success) {
        _startPeriodicSync();
        debugPrint('✅ ServerTimeService: Initialized successfully');
      } else {
        debugPrint('⚠️ ServerTimeService: Failed to initialize, will use device time');
      }
      
      _initializationCompleter!.complete();
    } catch (e) {
      debugPrint('❌ ServerTimeService: Initialization error: $e');
      _initializationCompleter!.complete();
    }
  }

  /// Manually triggers a time synchronization.
  /// Returns true if synchronization was successful.
  Future<bool> sync() async {
    debugPrint('🔄 ServerTimeService: Manual sync requested');
    return await _performSync();
  }

  /// Disposes the service and stops periodic synchronization.
  void dispose() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = null;
    debugPrint('🛑 ServerTimeService: Disposed');
  }

  /// Performs the actual time synchronization with retry logic.
  Future<bool> _performSync() async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        debugPrint('🔄 ServerTimeService: Sync attempt $attempt/$_maxRetries');
        
        final deviceTimeBeforeRequest = DateTime.now();
        
        final result = await _fetchServerTime()
            .timeout(_syncTimeout)
            .run();
        
        final deviceTimeAfterRequest = DateTime.now();
        
        return result.fold(
          (error) {
            debugPrint('❌ ServerTimeService: Sync failed - $error');
            return false;
          },
          (serverTime) {
            // Calculate network latency and adjust server time
            final networkLatency = deviceTimeAfterRequest
                .difference(deviceTimeBeforeRequest);
            final adjustedServerTime = serverTime.add(networkLatency ~/ 2);
            
            // Calculate the offset
            final newOffset = adjustedServerTime.difference(deviceTimeAfterRequest);
            
            _timeOffset = newOffset;
            _lastSyncTime = DateTime.now();
            _isInitialized = true;
            
            debugPrint('✅ ServerTimeService: Sync successful');
            debugPrint('   Server time: $adjustedServerTime');
            debugPrint('   Device time: $deviceTimeAfterRequest');
            debugPrint('   Time offset: ${newOffset.inMilliseconds}ms');
            debugPrint('   Network latency: ${networkLatency.inMilliseconds}ms');
            
            return true;
          },
        );
      } catch (e) {
        debugPrint('❌ ServerTimeService: Sync attempt $attempt failed - $e');
        
        if (attempt < _maxRetries) {
          // Exponential backoff
          final delay = Duration(seconds: pow(2, attempt).toInt());
          await Future.delayed(delay);
        }
      }
    }
    
    debugPrint('❌ ServerTimeService: All sync attempts failed');
    return false;
  }

  /// Starts periodic synchronization to keep time offset accurate.
  void _startPeriodicSync() {
    _periodicSyncTimer?.cancel();
    
    _periodicSyncTimer = Timer.periodic(_syncInterval, (timer) async {
      debugPrint('⏰ ServerTimeService: Periodic sync triggered');
      await _performSync();
    });
    
    debugPrint('⏰ ServerTimeService: Periodic sync started (${_syncInterval.inMinutes} min intervals)');
  }
}

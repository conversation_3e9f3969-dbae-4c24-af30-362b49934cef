import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/domain/repository/lean_repository.dart';
import 'package:prelude/prelude.dart';

class GetLeanAccountUsecase {
  final LeanRepository _leanRepository;
  const GetLeanAccountUsecase(this._leanRepository);
  TaskEither<Exception, LeanCustomerResponse> call() {
    return _leanRepository.getLeanAccounts();
  }
}

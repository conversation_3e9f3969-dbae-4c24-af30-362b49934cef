import 'package:payment/src/data/bank_account_lean_response/bank_account_lean_response.dart';
import 'package:payment/src/domain/repository/lean_repository.dart';
import 'package:prelude/prelude.dart';

class GetLeanBankAccountForAdditionUsecase {
  final LeanRepository leanRepository;

  const GetLeanBankAccountForAdditionUsecase({required this.leanRepository});

  TaskEither<Exception, BankAccountLeanResponse> call() {
    return leanRepository.getBankAccountForLeanAccountAddition();
  }
}

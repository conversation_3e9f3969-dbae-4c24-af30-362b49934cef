import 'package:payment/src/data/delete_lean_payment_source_response/delete_lean_payment_source_response.dart';
import 'package:payment/src/domain/repository/lean_repository.dart';
import 'package:prelude/prelude.dart';

class DeleteLeanPaymentSourceUsecase {
  final LeanRepository _leanRepository;
  const DeleteLeanPaymentSourceUsecase(this._leanRepository);
  TaskEither<Exception, DeleteLeanPaymentSourceResponse> call({
    required String paymentSourceId,
  }) {
    return _leanRepository.deleteLeanPaymentSource(
      paymentSourceId: paymentSourceId,
    );
  }
}

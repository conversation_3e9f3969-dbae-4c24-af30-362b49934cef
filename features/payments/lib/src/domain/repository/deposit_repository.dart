import 'package:payment/src/data/deposit_request_model.dart';
import 'package:payment/src/data/deposit_response.dart';

import 'package:payment/src/domain/exceptions/deposit_exception/deposit_exception.dart';
import 'package:api_client/api_client.dart';

import 'package:prelude/prelude.dart';

class DepositRepository {
  final ApiClientBase apiClient;

  const DepositRepository({required this.apiClient});

  TaskEither<Exception, DepositResponse> getDepositDetails({
    required DepositRequestModel data,
  }) {
    return apiClient
        .post<Map<String, dynamic>>(
          '/api/v1/payment/deposit',
          data: data.toJson(),
          // TODO (aakash): Replace correlation id
          headers: {"X-Correlation-Id": "12345"},
        )
        .mapLeft((error) {
          if (error is ClientException) {
            return DepositException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final depositResponse = DepositResponse.fromJson(response.data!);
              if (!depositResponse.success) {
                throw DepositException.unknown(
                  code: 0,
                  message: "Failed to get deposit details ${depositResponse}",
                );
              }
              return depositResponse;
            },
            (error, stackTrace) {
              if (error is DepositException) {
                return error;
              }
              return Exception(error);
            },
          );
        });
  }
}

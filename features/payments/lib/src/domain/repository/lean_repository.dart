import 'dart:developer';

import 'package:api_client/api_client.dart';
import 'package:payment/src/data/bank_account_lean_response/bank_account_lean_response.dart';
import 'package:payment/src/data/delete_lean_payment_source_response/delete_lean_payment_source_response.dart';
import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/domain/exceptions/delete_lean_payment_source_exception/delete_lean_payment_source_exception.dart';
import 'package:payment/src/domain/exceptions/get_bank_account_for_lean_account_addition_exception/get_bank_account_for_lean_account_addition_exception.dart';
import 'package:payment/src/domain/exceptions/get_lean_account_exception/get_lean_account_exception.dart';
import 'package:prelude/prelude.dart';

class LeanRepository {
  final ApiClientBase apiClient;

  const LeanRepository({required this.apiClient});

  TaskEither<Exception, LeanCustomerResponse> getLeanAccounts() {
    return apiClient
        .get<Map<String, dynamic>>('/api/v1/psp/lean/accounts')
        .mapLeft((error) {
          if (error is ClientException) {
            return GetLeanAccountException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final leanCustomerResponse = LeanCustomerResponse.fromJson(
                response.data!,
              );
              if (!leanCustomerResponse.success) {
                throw GetLeanAccountException.unknown(
                  code: 0,
                  message:
                      "Failed to get lean account details ${leanCustomerResponse}",
                );
              }
              return leanCustomerResponse;
            },
            (error, stackTrace) {
              log("Error in parsing lean account response: $error");
              return GetLeanAccountException.unknown(
                code: 0,
                message: "Failed to get lean account details.",
              );
            },
          );
        });
  }

  TaskEither<Exception, BankAccountLeanResponse>
  getBankAccountForLeanAccountAddition() {
    return apiClient
        .get<Map<String, dynamic>>('/api/v1/psp/lean/banks')
        .mapLeft((error) {
          if (error is ClientException) {
            return GetBankAccountForLeanAccountAdditionException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final bankAccountLeanResponse = BankAccountLeanResponse.fromJson(
                response.data!,
              );
              if (!bankAccountLeanResponse.success) {
                throw GetBankAccountForLeanAccountAdditionException.unknown(
                  code: 0,
                  message:
                      "Failed to get bank account for lean account addition ${bankAccountLeanResponse}",
                );
              }
              return bankAccountLeanResponse;
            },
            (error, stackTrace) {
              log(
                "Error in parsing bank account for lean account addition response: $error",
              );
              return GetBankAccountForLeanAccountAdditionException.unknown(
                code: 0,
                message:
                    "Failed to get bank account for lean account addition.",
              );
            },
          );
        });
  }

  TaskEither<Exception, DeleteLeanPaymentSourceResponse>
  deleteLeanPaymentSource({required String paymentSourceId}) {
    return apiClient
        .post<Map<String, dynamic>>(
          '/api/v1/psp/lean/delete-payment-source',
          data: {"paymentSourceId": paymentSourceId},
        )
        .mapLeft((error) {
          if (error is ClientException) {
            return DeleteLeanPaymentSourceException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final deleteResponse = DeleteLeanPaymentSourceResponse.fromJson(
                response.data!,
              );
              if (!deleteResponse.success) {
                throw DeleteLeanPaymentSourceException.unknown(
                  code: 0,
                  message: "Failed to delete payment source ${deleteResponse}",
                );
              }
              return deleteResponse;
            },
            (error, stackTrace) {
              log("Error in parsing delete payment source response: $error");
              return DeleteLeanPaymentSourceException.unknown(
                code: 0,
                message: "Failed to delete payment source.",
              );
            },
          );
        });
  }
}

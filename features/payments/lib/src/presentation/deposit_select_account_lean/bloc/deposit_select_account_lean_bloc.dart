import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/usecase/delete_lean_payment_source_usecase.dart';
import 'package:payment/src/domain/usecase/get_lean_account_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

part 'deposit_select_account_lean_bloc.freezed.dart';
part 'deposit_select_account_lean_event.dart';
part 'deposit_select_account_lean_state.dart';

class DepositSelectAccountLeanBloc
    extends Bloc<DepositSelectAccountLeanEvent, DepositSelectAccountLeanState> {
  final GetLeanAccountUsecase _getLeanAccountUsecase;
  final DeleteLeanPaymentSourceUsecase _deleteLeanPaymentSourceUsecase;
  final PaymentNavigation _paymentNavigation;
  final DepositAnalyticsEvent _depositAnalyticsEvent;
  DepositSelectAccountLeanBloc({
    required GetLeanAccountUsecase getLeanAccountUsecase,
    required DeleteLeanPaymentSourceUsecase deleteLeanPaymentSourceUsecase,
    required PaymentNavigation paymentNavigation,
    required DepositAnalyticsEvent depositAnalyticsEvent,
  }) : _getLeanAccountUsecase = getLeanAccountUsecase,
       _deleteLeanPaymentSourceUsecase = deleteLeanPaymentSourceUsecase,
       _paymentNavigation = paymentNavigation,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       super(const DepositSelectAccountLeanState()) {
    on<_InitArgumentsEvent>(_initArguments);
    on<_OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    on<_OnAddAccountPressedEvent>(_onAddAccountPressed);
    on<_GetLeanAccountsEvent>(_onGetLeanAccounts);
    on<_OnLeanBankAccountSelectedEvent>(_onLeanBankAccountSelected);
    on<_OnEditAccountsModeToggledEvent>(_onEditAccountsModeToggled);
    on<_OnLeanPaymentSourceDeletedEvent>(_onLeanPaymentSourceDeleted);
  }
  FutureOr<void> _initArguments(
    _InitArgumentsEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    emit(
      state.copyWith(
        depositPaymentMethod: event.paymentMethod,
        depositFlowConfig: event.depositFlowConfig,
      ),
    );
  }

  Future<void> _onGetLeanAccounts(
    _GetLeanAccountsEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) async {
    emit(state.copyWith(currentState: const LeanAccountProcessState.loading()));
    // analytics: mark start of loading payment sources request
    _depositAnalyticsEvent.depositLeanPaymentSourcesLoaded(sourcesCount: 0);
    final result = await _getLeanAccountUsecase().run();
    if (isClosed) return;
    result.fold(
      (error) {
        //todo: handle error state
        log('Error fetching lean accounts: $error');
        _depositAnalyticsEvent.depositLeanPaymentSourcesLoadError(
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: LeanAccountProcessState.error()));
      },
      (response) {
        log('Lean accounts fetched: ${response.toJson()}');
        final sources = response.data.paymentSourceList;
        _depositAnalyticsEvent.depositLeanPaymentSourcesLoaded(
          sourcesCount: sources.length,
        );
        final noAccountsFound = sources.isEmpty;
        final currentState =
            noAccountsFound
                ? LeanAccountProcessState.empty()
                : LeanAccountProcessState.loaded();
        emit(
          state.copyWith(
            currentState: currentState,
            leanCustomerResponse: response,
            isEditingAccountsMode: false,
            selectedAccount: null,
          ),
        );
      },
    );
  }

  FutureOr<void> _onAddAccountPressed(
    _OnAddAccountPressedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    log('Add account button pressed');
    if (state.leanCustomerResponse?.data != null) {
      assert(
        state.depositPaymentMethod != null,
        "Deposit payment method is null",
      );
      _paymentNavigation.navigateToLeanConnectBank(
        leanAccountArgs: LeanAccountArgs(
          selectedLeanAccount: state.selectedAccount,
          leanCustomerData: state.leanCustomerResponse!.data,
          depositPaymentMethod: state.depositPaymentMethod!,
        ),
        then: () {
          log('Returned from Lean Connect Bank screen, refreshing accounts');
          add(const DepositSelectAccountLeanEvent.getLeanAccounts());
        },
      );
    } else {
      log('Lean customer data is null, cannot navigate to add account');
    }
  }

  FutureOr<void> _onLeanBankAccountSelected(
    _OnLeanBankAccountSelectedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    if (event.selectedAccount == state.selectedAccount) {
      emit(state.copyWith(selectedAccount: null));
    } else {
      // optional: analytics on confirm happens at selection tap in widget; nothing here
      emit(state.copyWith(selectedAccount: event.selectedAccount));
    }
  }

  FutureOr<void> _onConfirmButtonPressed(
    _OnConfirmButtonPressedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    log('Confirm button pressed');
    _paymentNavigation.goToDepositSelectAccountAndAmountScreen(
      state.depositPaymentMethod!,
      depositFlowConfig: state.depositFlowConfig!,
      leanAccountArgs: LeanAccountArgs(
        selectedLeanAccount: state.selectedAccount,
        leanCustomerData: state.leanCustomerResponse!.data,
        depositPaymentMethod: state.depositPaymentMethod!,
      ),
    );
  }

  FutureOr<void> _onEditAccountsModeToggled(
    _OnEditAccountsModeToggledEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    emit(state.copyWith(isEditingAccountsMode: !state.isEditingAccountsMode));
  }

  Future<void> _onLeanPaymentSourceDeleted(
    _OnLeanPaymentSourceDeletedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) async {
    emit(state.copyWith(currentState: const LeanAccountProcessState.loading()));
    _depositAnalyticsEvent.depositLeanPaymentSourceDeleteStart(
      paymentSourceId: event.paymentSource.id,
    );
    final result =
        await _deleteLeanPaymentSourceUsecase(
          paymentSourceId: event.paymentSource.id,
        ).run();
    if (isClosed) return;
    result.fold(
      (error) {
        log('Error deleting payment source: $error');
        _depositAnalyticsEvent.depositLeanPaymentSourceDeleteError(
          paymentSourceId: event.paymentSource.id,
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: LeanAccountProcessState.error()));
      },
      (response) {
        log('Payment source deleted successfully: ${response.toJson()}');
        _depositAnalyticsEvent.depositLeanPaymentSourceDeleteSuccess(
          paymentSourceId: event.paymentSource.id,
        );
        // Remove the payment source from the current state
        if (state.leanCustomerResponse != null) {
          final updatedPaymentSources = List<PaymentSource>.of(
            state.leanCustomerResponse!.data.paymentSourceList,
          )..removeWhere((ps) => ps.id == event.paymentSource.id);

          final updatedLeanCustomerResponse = state.leanCustomerResponse!
              .copyWith(
                data: state.leanCustomerResponse!.data.copyWith(
                  paymentSourceList: updatedPaymentSources,
                ),
              );

          final noAccountsFound = updatedPaymentSources.isEmpty;
          final currentState =
              noAccountsFound
                  ? LeanAccountProcessState.empty()
                  : LeanAccountProcessState.loaded();

          emit(
            state.copyWith(
              currentState: currentState,
              leanCustomerResponse: updatedLeanCustomerResponse,
            ),
          );
        } else {
          emit(state.copyWith(currentState: LeanAccountProcessState.loaded()));
        }
      },
    );
  }
}

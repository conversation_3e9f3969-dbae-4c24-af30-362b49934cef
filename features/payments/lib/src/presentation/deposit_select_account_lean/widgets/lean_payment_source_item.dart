// ignore_for_file: prefer-single-widget-per-file

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment;

import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';

class LeanPaymentSourceItem extends StatelessWidget {
  const LeanPaymentSourceItem({
    super.key,
    required this.paymentSource,
    required this.selectedAccount,
    required this.isEditingAccountsMode,
    required this.onAccountSelected,
    required this.onPaymentSourceDeleted,
  });
  final PaymentSource paymentSource;
  final LeanAccount? selectedAccount;
  final void Function(LeanAccount selectedAccount) onAccountSelected;
  final void Function() onPaymentSourceDeleted;
  final bool isEditingAccountsMode;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.border.borderSecondary),
        borderRadius: BorderRadius.circular(DuploRadius.radius_xl_12),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: DuploSpacing.spacing_3xl_24,
              bottom: DuploSpacing.spacing_lg_12,
              right: DuploSpacing.spacing_xl_16,
              left: DuploSpacing.spacing_xl_16,
            ),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
                  child: Container(
                    color: theme.background.bgTertiary,
                    height: 48,
                    width: 48,
                    child: Center(
                      child: payment.Assets.images.bankPlaceholder.svg(
                        height: 32,
                        width: 32,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DuploSpacing.spacing_lg_12,
                    ),
                    child: DuploText(
                      //todo(sambhav): update with bank name after we get this from api
                      text: paymentSource.bankIdentifier,
                      style: textStyle.textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textSecondary,
                    ),
                  ),
                ),
                isEditingAccountsMode
                    ? DuploTap(
                      onTap: () => onPaymentSourceDeleted(),
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Assets.images.trash.svg(
                          width: 20,
                          height: 20,
                          colorFilter: ColorFilter.mode(
                            theme.button.buttonTertiaryErrorFg,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    )
                    : SizedBox.shrink(),
              ],
            ),
          ),
          Divider(color: theme.border.borderSecondary),
          ListView.separated(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (ctx, index) {
              final account = paymentSource.accountList[index];
              return LeanAccountListItem(
                key: Key('leanAccount_${account.accountId}'),
                account: account,
                isEditingAccountsMode: isEditingAccountsMode,
                selectedAccount: selectedAccount,
                onAccountSelected: () => onAccountSelected(account),
              );
            },
            separatorBuilder: (ctx, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: DuploSpacing.spacing_3xl_24,
                ),
                child: Divider(color: theme.border.borderSecondary),
              );
            },
            itemCount: paymentSource.accountList.length,
          ),
          SizedBox(height: DuploSpacing.spacing_lg_12),
        ],
      ),
    );
  }
}

class SavedBankAccountBaseChipParent extends StatelessWidget {
  const SavedBankAccountBaseChipParent({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DuploSpacing.spacing_sm_6,
        vertical: DuploSpacing.spacing_xxs_2,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: theme.utility.utilityGray200),
        color: theme.utility.utilityGray50,
        borderRadius: BorderRadius.circular(DuploRadius.radius_xxs_2),
      ),
      child: child,
    );
  }
}

class LeanAccountListItem extends StatelessWidget {
  const LeanAccountListItem({
    super.key,
    required this.account,
    required this.selectedAccount,
    required this.onAccountSelected,
    required this.isEditingAccountsMode,
  });
  final LeanAccount account;
  final LeanAccount? selectedAccount;
  final VoidCallback onAccountSelected;
  final bool isEditingAccountsMode;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    return DuploTap(
      onTap: onAccountSelected,
      child: Container(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: DuploSpacing.spacing_3xl_24,
            vertical: DuploSpacing.spacing_lg_12,
          ),
          child: Row(
            children: [
              Expanded(
                child: Wrap(
                  alignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  runSpacing: 4,
                  children: [
                    DuploText(
                      text: "${account.accountName} - ",
                      style: textStyle.textSm,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textSecondary,
                    ),
                    SavedBankAccountBaseChipParent(
                      child: DuploText(
                        text: account.accountNumber,
                        style: textStyle.textXs,
                        fontWeight: DuploFontWeight.medium,
                        color: theme.utility.utilityGray700,
                      ),
                    ),
                    SizedBox(width: DuploSpacing.spacing_sm_6),
                    SavedBankAccountBaseChipParent(
                      child: DuploText(
                        text: account.currency,
                        style: textStyle.textXs,
                        fontWeight: DuploFontWeight.medium,
                        color: theme.utility.utilityGray700,
                      ),
                    ),
                  ],
                ),
              ),
              isEditingAccountsMode
                  ? SizedBox.shrink()
                  : selectedAccount?.accountId == account.accountId
                  ? Assets.images.checkboxBase.svg(height: 24, width: 24)
                  : Container(
                    height: 24,
                    width: 24,
                    decoration: BoxDecoration(
                      border: Border.all(color: theme.border.borderPrimary),
                      borderRadius: BorderRadius.circular(
                        DuploRadius.radius_sm_6,
                      ),
                    ),
                  ),
            ],
          ),
        ),
      ),
    );
  }
}

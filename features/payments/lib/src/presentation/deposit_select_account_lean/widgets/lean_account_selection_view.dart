import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment_assets;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/bloc/deposit_select_account_lean_bloc.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/widgets/lean_payment_source_item.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/di/di_container.dart';

class LeanAccountSelectionView extends StatelessWidget {
  const LeanAccountSelectionView({super.key, required this.paymentMethod});
  final DepositPaymentMethod paymentMethod;

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localization.payments_lean_deposit_with_payment_method(
          paymentMethod.name,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DuploSpacing.spacing_md_8,
            ),
            child: DuploTap(
              key: const Key('editIcon'),
              onTap: () {
                context.read<DepositSelectAccountLeanBloc>().add(
                  DepositSelectAccountLeanEvent.onEditAccountsModeToggled(),
                );
              },
              child: Container(
                height: 40,
                width: 40,
                child: Center(
                  child: BlocBuilder<
                    DepositSelectAccountLeanBloc,
                    DepositSelectAccountLeanState
                  >(
                    buildWhen:
                        (previous, current) =>
                            current.isEditingAccountsMode !=
                            previous.isEditingAccountsMode,
                    builder: (blocContext, state) {
                      return state.isEditingAccountsMode
                          ? Assets.images.closeIc.svg(
                            width: 16,
                            height: 16,
                            colorFilter: ColorFilter.mode(
                              theme.foreground.fgSecondary,
                              BlendMode.srcIn,
                            ),
                          )
                          : payment_assets.Assets.images.editIcon.svg(
                            width: 22,
                            height: 22,
                            colorFilter: ColorFilter.mode(
                              theme.foreground.fgSecondary,
                              BlendMode.srcIn,
                            ),
                          );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.payments_saved_bank_accounts,
                        style: textStyle.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      SizedBox(height: DuploSpacing.spacing_3xl_24),
                      BlocBuilder<
                        DepositSelectAccountLeanBloc,
                        DepositSelectAccountLeanState
                      >(
                        buildWhen:
                            (previous, current) =>
                                current.leanCustomerResponse !=
                                    previous.leanCustomerResponse ||
                                current.selectedAccount !=
                                    previous.selectedAccount ||
                                current.isEditingAccountsMode !=
                                    previous.isEditingAccountsMode,
                        builder: (blocCtx, state) {
                          final paymentSourceList =
                              state
                                  .leanCustomerResponse
                                  ?.data
                                  .paymentSourceList ??
                              [];
                          return ListView.separated(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: paymentSourceList.length,
                            separatorBuilder:
                                (_, __) => SizedBox(
                                  height: DuploSpacing.spacing_lg_12,
                                ),
                            itemBuilder: (ctx, index) {
                              final paymentSource = paymentSourceList[index];
                              return LeanPaymentSourceItem(
                                paymentSource: paymentSource,
                                selectedAccount: state.selectedAccount,
                                isEditingAccountsMode:
                                    state.isEditingAccountsMode,
                                onAccountSelected: (selectedAccount) {
                                  // analytics: payment source selected
                                  final acc = selectedAccount;
                                  final last4 =
                                      acc.iban.length >= 4
                                          ? acc.iban.substring(
                                            acc.iban.length - 4,
                                          )
                                          : acc.iban;
                                  diContainer<DepositAnalyticsEvent>()
                                      .depositLeanPaymentSourceSelected(
                                        paymentSourceId: paymentSource.id,
                                        bankIdentifier:
                                            paymentSource.bankIdentifier,
                                        accountId: acc.accountId,
                                        ibanLast4: last4,
                                      );
                                  blocCtx.read<DepositSelectAccountLeanBloc>().add(
                                    DepositSelectAccountLeanEvent.onLeanBankAccountSelected(
                                      selectedAccount: selectedAccount,
                                    ),
                                  );
                                },
                                onPaymentSourceDeleted: () {
                                  _showDeleteAccountBottomSheet(
                                    context,
                                    blocCtx,
                                    paymentSource,
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),

              BlocBuilder<
                DepositSelectAccountLeanBloc,
                DepositSelectAccountLeanState
              >(
                buildWhen:
                    (previous, current) =>
                        current.selectedAccount != previous.selectedAccount ||
                        current.isEditingAccountsMode !=
                            previous.isEditingAccountsMode,
                builder: (blocContext, state) {
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: DuploSpacing.spacing_xl_16,
                        ),
                        child: DuploButton.secondary(
                          useFullWidth: true,
                          title: localization.payments_add_new_account,
                          leadingIcon: Assets.images.plus.keyName,
                          onTap: () {
                            blocContext.read<DepositSelectAccountLeanBloc>().add(
                              const DepositSelectAccountLeanEvent.onAddAccountPressed(),
                            );
                          },
                        ),
                      ),
                      DuploButton.defaultPrimary(
                        useFullWidth: true,
                        title: localization.payments_continue,
                        isDisabled:
                            state.selectedAccount == null ||
                            state.isEditingAccountsMode,
                        trailingIcon:
                            Assets.images
                                .chevronRightDirectional(context)
                                .keyName,
                        onTap: () {
                          blocContext.read<DepositSelectAccountLeanBloc>().add(
                            const DepositSelectAccountLeanEvent.onConfirmButtonPressed(),
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteAccountBottomSheet(
    BuildContext context,
    BuildContext blocContext,
    PaymentSource paymentSource,
  ) {
    final localization = EquitiLocalization.of(context);

    DuploErrorSheet.show<void>(
      context: context,
      bodyTitle: localization.payments_delete_lean_account_title,
      bodySubTitle: localization.payments_delete_lean_account_description,
      hideCloseButton: true,
      crossAxisAlignment: CrossAxisAlignment.start,
      customIcon: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: context.duploTheme.background.bgErrorSecondary,
          shape: BoxShape.circle,
        ),
        child: payment_assets.Assets.images.deleteIc.svg(
          width: 18,
          height: 18,
          colorFilter: ColorFilter.mode(
            context.duploTheme.icon.iconFeaturedLightFgError,
            BlendMode.srcIn,
          ),
        ),
      ),
      actionWidget: Column(
        children: [
          DuploButton.sellPrimary(
            title: localization.payments_delete_lean_account_button,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
              blocContext.read<DepositSelectAccountLeanBloc>().add(
                DepositSelectAccountLeanEvent.onLeanPaymentSourceDeleted(
                  paymentSource: paymentSource,
                ),
              );
            },
          ),
          SizedBox(height: DuploSpacing.spacing_md_8),
          DuploButton.secondary(
            title: localization.payments_cancel,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/bank_account_lean_response/bank_account_lean_response.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/presentation/add_connect_lean_account/bloc/add_connect_lean_account_bloc.dart';
import 'package:payment/src/presentation/add_connect_lean_account/widgets/lean_bank_account_item.dart';
import 'package:payment/src/presentation/widgets/lean/lean_payment.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';

class AddConnectLeanAccountLoadedState extends StatefulWidget {
  const AddConnectLeanAccountLoadedState({
    super.key,
    required this.paymentMethod,
  });
  final DepositPaymentMethod paymentMethod;

  @override
  State<AddConnectLeanAccountLoadedState> createState() =>
      _AddConnectLeanAccountLoadedStateState();
}

class _AddConnectLeanAccountLoadedStateState
    extends State<AddConnectLeanAccountLoadedState> {
  final TextEditingController searchTextController = TextEditingController();

  @override
  void dispose() {
    searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localization.payments_lean_deposit_with_payment_method(
          widget.paymentMethod.name,
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    top: DuploSpacing.spacing_4xl_32,
                    bottom: DuploSpacing.spacing_3xl_24,
                  ),
                  child: Column(
                    children: [
                      DuploText(
                        text: localization.payments_lean_select_your_bank,
                        style: textStyle.textXl,
                        color: theme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                      ),
                      SizedBox(height: DuploSpacing.spacing_md_8),
                      DuploText(
                        text:
                            localization.payments_lean_select_bank_description,
                        style: textStyle.textSm,
                        color: theme.text.textSecondary,
                        fontWeight: DuploFontWeight.regular,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                BlocBuilder<
                  AddConnectLeanAccountBloc,
                  AddConnectLeanAccountState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.filteredBankAccountList?.length !=
                          current.filteredBankAccountList?.length,
                  builder: (blocCtx, state) {
                    final bloc = blocCtx.read<AddConnectLeanAccountBloc>();
                    return Column(
                      children: [
                        DuploSearchInputField(
                          controller: searchTextController,
                          onChanged:
                              (value) => bloc.add(
                                AddConnectLeanAccountEvent.searchBankAccounts(
                                  value,
                                ),
                              ),
                        ),
                        SizedBox(height: DuploSpacing.spacing_lg_12),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (ctx, index) {
                            final item = state.filteredBankAccountList
                                ?.elementAtOrNull(index);
                            return item == null
                                ? SizedBox.shrink()
                                : DuploTap(
                                  onTap:
                                      () => _onLeanBankAccountPressed(
                                        bloc,
                                        item,
                                        context,
                                      ),
                                  child: LeanBankAccountItem(item: item),
                                );
                          },
                          separatorBuilder: (ctx, index) {
                            return Container(
                              height: 1,
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(
                                vertical: DuploSpacing.spacing_xs_4,
                              ),
                              color: theme.border.borderSecondary,
                            );
                          },
                          itemCount: state.filteredBankAccountList?.length ?? 0,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onLeanBankAccountPressed(
    AddConnectLeanAccountBloc bloc,
    LeanBankAccount item,
    BuildContext context,
  ) {
    final state = bloc.state;
    final analytics = diContainer<DepositAnalyticsEvent>();
    analytics.depositLeanBankSelected(
      bankIdentifier: item.identifier,
      bankDisplayName: item.name,
    );
    analytics.depositLeanConnectStart(bankIdentifier: item.identifier);
    LeanPayment.connect(
          context: context,
          appToken: state.addLeanAccountArgs!.leanCustomerData.appToken,
          customerId: state.addLeanAccountArgs!.leanCustomerData.customerId,
          accessToken: state.addLeanAccountArgs!.leanCustomerData.customerToken,
          bankIdentifier: item.identifier,
        )
        .then((value) {
          if (value == true) {
            analytics.depositLeanConnectSuccess(
              bankIdentifier: item.identifier,
            );
            Navigator.pop(context, true);
          } else {
            analytics.depositLeanConnectCancelled(
              bankIdentifier: item.identifier,
            );
          }
        })
        .catchError((Object e) {
          analytics.depositLeanConnectError(
            bankIdentifier: item.identifier,
            errorMessage: e.toString(),
          );
        });
  }
}

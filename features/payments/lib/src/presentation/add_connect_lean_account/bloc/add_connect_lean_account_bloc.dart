import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/bank_account_lean_response/bank_account_lean_response.dart';
import 'package:payment/src/domain/usecase/get_lean_bank_account_for_addition_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';

part 'add_connect_lean_account_bloc.freezed.dart';
part 'add_connect_lean_account_event.dart';
part 'add_connect_lean_account_state.dart';

class AddConnectLeanAccountBloc
    extends Bloc<AddConnectLeanAccountEvent, AddConnectLeanAccountState> {
  final PaymentNavigation _paymentNavigation;
  final GetLeanBankAccountForAdditionUsecase
  _getLeanBankAccountForAdditionUsecase;
  final DepositAnalyticsEvent _depositAnalyticsEvent;
  AddConnectLeanAccountBloc({
    required GetLeanBankAccountForAdditionUsecase
    getLeanBankAccountForAdditionUsecase,
    required PaymentNavigation paymentNavigation,
    required DepositAnalyticsEvent depositAnalyticsEvent,
  }) : _getLeanBankAccountForAdditionUsecase =
           getLeanBankAccountForAdditionUsecase,
       _paymentNavigation = paymentNavigation,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       super(const AddConnectLeanAccountState()) {
    on<_InitArgumentsEvent>(_onInitArguments);
    on<_GetLeanBanksEvent>(_onGetLeanBanks);
    on<_SearchBankAccountsEvent>(_onSearchBankAccounts);
  }
  FutureOr<void> _onInitArguments(
    _InitArgumentsEvent event,
    Emitter<AddConnectLeanAccountState> emit,
  ) {
    emit(state.copyWith(addLeanAccountArgs: event.addLeanAccountArgs));
  }

  Future<void> _onGetLeanBanks(
    _GetLeanBanksEvent event,
    Emitter<AddConnectLeanAccountState> emit,
  ) async {
    emit(
      state.copyWith(currentState: const AddLeanAccountProcessState.loading()),
    );
    final result = await _getLeanBankAccountForAdditionUsecase().run();
    if (isClosed) return;
    result.fold(
      (error) {
        //todo: handle error state
        log('Error fetching lean bank accounts: $error');
        // analytics: bank list load error
        _depositAnalyticsEvent.depositLeanBankListLoadError(
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: AddLeanAccountProcessState.error()));
      },
      (response) {
        log('Lean bank accounts fetched: ${response.toJson()}');
        _depositAnalyticsEvent.depositLeanBankListLoaded(
          banksCount: response.accounts.length,
        );
        final noAccountsFound = response.accounts.isEmpty;
        final currentState =
            noAccountsFound
                ? AddLeanAccountProcessState.empty()
                : AddLeanAccountProcessState.loaded();
        emit(
          state.copyWith(
            currentState: currentState,
            bankAccountLeanResponse: response,
            filteredBankAccountList: response.accounts,
          ),
        );
      },
    );
  }

  FutureOr<void> _onSearchBankAccounts(
    _SearchBankAccountsEvent event,
    Emitter<AddConnectLeanAccountState> emit,
  ) {
    final query = event.query.toLowerCase();
    final allAccounts = state.bankAccountLeanResponse?.accounts ?? [];
    final filteredAccounts =
        allAccounts.where((account) {
          final bankName = account.name.toLowerCase();
          return bankName.contains(query);
        }).toList();
    emit(state.copyWith(filteredBankAccountList: filteredAccounts));
  }
}

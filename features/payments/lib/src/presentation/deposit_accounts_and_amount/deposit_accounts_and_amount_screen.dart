import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/presentation/widgets/lean/lean_payment.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/widgets/deposit_accounts_amount_loaded_screen.dart';

import 'package:payment/src/presentation/widgets/transaction_status_screen.dart';

import 'bloc/deposit_accounts_and_amount_bloc.dart';

class DepositAccountsAndAmountScreen extends StatelessWidget {
  const DepositAccountsAndAmountScreen({
    super.key,
    required this.paymentMethod,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
    required this.depositFlowConfig,
    this.leanAccountArgs,
  });
  final DepositPaymentMethod paymentMethod;
  final DepositFlowConfig depositFlowConfig;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;
  final LeanAccountArgs? leanAccountArgs;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (ctx) =>
              diContainer<DepositAccountsAndAmountBloc>()..add(
                DepositAccountsAndAmountEvent.initArgs(
                  maxPollingAttempts: maxPollingAttempts,
                  pollingFrequencySeconds: pollingFrequencySeconds,
                  depositFlowConfig: depositFlowConfig,
                  leanAccountArgs: leanAccountArgs,
                ),
              ),
      child: BlocConsumer<
        DepositAccountsAndAmountBloc,
        DepositAccountsAndAmountState
      >(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState ||
                previous.isFullPageLoadingEnabled !=
                    current.isFullPageLoadingEnabled,
        listener: (listenerContext, state) {
          if (state.processState is StartLeanPaymentState) {
            final leanState = state.processState as StartLeanPaymentState;
            _openLeanPaymentBottomSheet(
              leanState: leanState,
              context: listenerContext,
            );
          }
        },
        builder: (ctx, state) {
          final bloc = ctx.read<DepositAccountsAndAmountBloc>();
          if (state.processState is PaymentSuccessState &&
              depositFlowConfig.depositType == DepositType.first) {
            diContainer<DepositAnalyticsEvent>().firstDepositComplete();
          }
          return switch (state.processState) {
            ErrorState() => const Center(child: Text('Error')),
            PaymentFailedState() => TransactionStatusScreen.error(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            PaymentRejectedState() => TransactionStatusScreen.declined(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
              onTryAgain: () {
                bloc.add(const ResetProcessStateEvent());
              },
              gatewayCode: null,
            ),
            PaymentSuccessState() => TransactionStatusScreen.success(
              context: context,
              onContinue: () {
                bloc.add(const OnPaymentSuccessContinuePressed());
              },
              onMakeAnotherDeposit: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            StartLeanPaymentState() ||
            LoadedState() => DepositAccountsAmountLoadedScreen(
              paymentMethod: paymentMethod,
              isFullScreenLoaderEnabled: state.isFullPageLoadingEnabled,
            ),
          };
        },
      ),
    );
  }

  Future<void> _openLeanPaymentBottomSheet({
    required StartLeanPaymentState leanState,
    required BuildContext context,
  }) async {
    final bloc = context.read<DepositAccountsAndAmountBloc>();

    final analytics = diContainer<DepositAnalyticsEvent>();
    await analytics.depositLeanPayStart(
      paymentIntentId: leanState.paymentIntentId,
      accountId: leanAccountArgs!.selectedLeanAccount?.id,
    );
    await analytics.depositLeanPayBottomSheetOpened(
      paymentIntentId: leanState.paymentIntentId,
    );
    if (leanAccountArgs!.selectedLeanAccount?.id != null) {
      await analytics.depositLeanAccountPrefilled(
        accountId: leanAccountArgs!.selectedLeanAccount!.id,
      );
    }
    try {
      await LeanPayment.pay(
        context: context,
        paymentIntentId: leanState.paymentIntentId,
        showLogs: true,
        accountId: leanAccountArgs!.selectedLeanAccount?.id,
        appToken: leanAccountArgs!.leanCustomerData.appToken,
        accessToken: leanAccountArgs!.leanCustomerData.customerToken,
        onCancelled: () {
          log('Lean connect action cancelled by user');
          analytics.depositLeanPayCancelled(
            paymentIntentId: leanState.paymentIntentId,
          );
        },
        onCallback: (status) {
          analytics.depositLeanPayCallback(
            paymentIntentId: leanState.paymentIntentId,
            status: status,
          );
          if (status == 'SUCCESS') {
            analytics.depositLeanPaySuccess(
              paymentIntentId: leanState.paymentIntentId,
            );
          } else {
            analytics.depositLeanPayFailure(
              paymentIntentId: leanState.paymentIntentId,
              failureReason: status,
            );
          }
          bloc.add(
            DepositAccountsAndAmountEvent.onLeanPaymentCallback(status: status),
          );
        },
        // customization is handled with sensible defaults inside LeanUI
      );
    } catch (e) {
      await analytics.depositLeanPayError(
        paymentIntentId: leanState.paymentIntentId,
        errorMessage: e.toString(),
      );
    }
  }
}

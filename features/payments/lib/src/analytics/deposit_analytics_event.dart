// ignore_for_file: prefer-sealed-bloc-events, prefer-immutable-bloc-events

import 'package:equiti_analytics/equiti_analytics.dart';

class DepositAnalyticsEvent {
  DepositAnalyticsEvent(this._analyticsService);

  final AnalyticsService _analyticsService;

  static const String _paymentType = 'deposit';

  String? _selectedMop;

  Map<String, dynamic> _getBaseAttributes() {
    return {
      'paymentType': _paymentType,
      if (_selectedMop != null) 'mop': _selectedMop,
    };
  }

  Future<bool> firstDepositStart() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.firstDepositStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.firstDepositStart.eventName,
    );
  }

  Future<bool> firstDepositComplete() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.firstDepositComplete.eventType.name,
      eventName: PaymentsAnalyticsEvent.firstDepositComplete.eventName,
    );
  }

  Future<bool> firstValueAction() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.firstValueAction.eventType.name,
      eventName: PaymentsAnalyticsEvent.firstValueAction.eventName,
    );
  }

  Future<bool> depositStart() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositStart.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositMopSelected(String mop) async {
    _selectedMop = mop;
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositMopSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositMopSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositAccountSelected({
    required String type,
    required String accountCurrency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositAccountSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositAccountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'type': type,
        'currency': accountCurrency,
      },
    );
  }

  Future<bool> depositCurrencyChanged({
    String? limits,
    required String accountCurrency,
    required String selectedCurrency,
    required double conversionRate,
    required String conversionRateString,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositCurrencyChanged.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositCurrencyChanged.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (limits != null) 'limits': limits,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
        'conversionRateString': conversionRateString,
      },
    );
  }

  Future<bool> depositSuggestedAmountSelected({
    required num amount,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositSuggestedAmountSelected.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositSuggestedAmountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'amount': amount,
        'currency': currency,
      },
    );
  }

  Future<bool> depositInitiated({
    required String transactionId,
    required String amount,
    required String convertedAmount,
    required String accountCurrency,
    required String selectedCurrency,
    required double conversionRate,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositInitiated.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositInitiated.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'transactionId': transactionId,
        'amount': amount,
        'convertedAmount': convertedAmount,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
      },
    );
  }

  Future<bool> depositWebviewLoadStarted() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositWebviewLoadStarted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewLoadStarted.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositWebviewLoaded() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositWebviewLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositWebviewCallback({
    required String status,
    required String gatewayCode,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositWebviewCallback.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewCallback.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'status': status,
        'gatewayCode': gatewayCode,
      },
    );
  }

  Future<bool> depositWebviewExitConfirmation({required bool userExit}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositWebviewExitConfirmation.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositWebviewExitConfirmation.eventName,
      metadata: {..._getBaseAttributes(), 'userExit': userExit},
    );
  }

  Future<bool> depositSdkLoadStarted() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkLoadStarted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkLoadStarted.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositSdkLoaded() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositSdkEvent({
    required String status,
    required String gatewayCode,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkEvent.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkEvent.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'status': status,
        'gatewayCode': gatewayCode,
      },
    );
  }

  Future<bool> depositCompleted({required String status}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositCompleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositCompleted.eventName,
      metadata: {..._getBaseAttributes(), 'status': status},
    );
  }

  Future<bool> depositBankDetailsCopied({
    required String bankName,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositBankDetailsCopied.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositBankDetailsCopied.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankName': bankName,
        'currency': currency,
      },
    );
  }

  Future<bool> depositBankPdfDownloaded({
    required String bankName,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositBankPdfDownloaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositBankPdfDownloaded.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankName': bankName,
        'currency': currency,
      },
    );
  }

  Future<bool> depositNotAvailable() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositNotAvailable.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositNotAvailable.eventName,
      metadata: {..._getBaseAttributes()},
    );
  }

  // Lean - Connect & Banks
  Future<bool> depositLeanBankListLoaded({required int banksCount}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanBankListLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanBankListLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'banksCount': banksCount},
    );
  }

  Future<bool> depositLeanBankListLoadError({
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanBankListLoadError.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanBankListLoadError.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }

  Future<bool> depositLeanBankSelected({
    required String bankIdentifier,
    String? bankDisplayName,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanBankSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanBankSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankIdentifier': bankIdentifier,
        if (bankDisplayName != null) 'bankDisplayName': bankDisplayName,
      },
    );
  }

  Future<bool> depositLeanConnectStart({required String bankIdentifier}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanConnectStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanConnectStart.eventName,
      metadata: {..._getBaseAttributes(), 'bankIdentifier': bankIdentifier},
    );
  }

  Future<bool> depositLeanConnectSuccess({
    required String bankIdentifier,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanConnectSuccess.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanConnectSuccess.eventName,
      metadata: {..._getBaseAttributes(), 'bankIdentifier': bankIdentifier},
    );
  }

  Future<bool> depositLeanConnectCancelled({
    required String bankIdentifier,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanConnectCancelled.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanConnectCancelled.eventName,
      metadata: {..._getBaseAttributes(), 'bankIdentifier': bankIdentifier},
    );
  }

  Future<bool> depositLeanConnectError({
    required String bankIdentifier,
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanConnectError.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanConnectError.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankIdentifier': bankIdentifier,
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }

  // Lean - Pay
  Future<bool> depositLeanPayBottomSheetOpened({
    required String paymentIntentId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanPayBottomSheetOpened.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPayBottomSheetOpened.eventName,
      metadata: {..._getBaseAttributes(), 'paymentIntentId': paymentIntentId},
    );
  }

  Future<bool> depositLeanPayStart({
    required String paymentIntentId,
    String? accountId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPayStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPayStart.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentIntentId': paymentIntentId,
        if (accountId != null) 'accountId': accountId,
      },
    );
  }

  Future<bool> depositLeanAccountPrefilled({required String accountId}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanAccountPrefilled.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanAccountPrefilled.eventName,
      metadata: {..._getBaseAttributes(), 'accountId': accountId},
    );
  }

  Future<bool> depositLeanPayCancelled({
    required String paymentIntentId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPayCancelled.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPayCancelled.eventName,
      metadata: {..._getBaseAttributes(), 'paymentIntentId': paymentIntentId},
    );
  }

  Future<bool> depositLeanPayCallback({
    required String paymentIntentId,
    required String status,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPayCallback.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPayCallback.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentIntentId': paymentIntentId,
        'status': status,
      },
    );
  }

  Future<bool> depositLeanPaySuccess({required String paymentIntentId}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPaySuccess.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPaySuccess.eventName,
      metadata: {..._getBaseAttributes(), 'paymentIntentId': paymentIntentId},
    );
  }

  Future<bool> depositLeanPayFailure({
    required String paymentIntentId,
    String? failureReason,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPayFailure.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPayFailure.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentIntentId': paymentIntentId,
        if (failureReason != null) 'failureReason': failureReason,
      },
    );
  }

  Future<bool> depositLeanPayError({
    required String paymentIntentId,
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanPayError.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanPayError.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentIntentId': paymentIntentId,
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }

  Future<bool> depositLeanAuthStep({
    required String stepName,
    required String status,
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositLeanAuthStep.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositLeanAuthStep.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'stepName': stepName,
        'status': status,
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }

  // Lean - Payment Sources
  Future<bool> depositLeanPaymentSourcesLoaded({
    required int sourcesCount,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositLeanPaymentSourcesLoaded.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPaymentSourcesLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'sourcesCount': sourcesCount},
    );
  }

  Future<bool> depositLeanPaymentSourcesLoadError({
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourcesLoadError
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPaymentSourcesLoadError.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }

  Future<bool> depositLeanPaymentSourceSelected({
    required String paymentSourceId,
    required String bankIdentifier,
    String? accountId,
    String? ibanLast4,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourceSelected
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPaymentSourceSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentSourceId': paymentSourceId,
        'bankIdentifier': bankIdentifier,
        if (accountId != null) 'accountId': accountId,
        if (ibanLast4 != null) 'ibanLast4': ibanLast4,
      },
    );
  }

  Future<bool> depositLeanPaymentSourceDeleteStart({
    required String paymentSourceId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourceDeleteStart
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPaymentSourceDeleteStart.eventName,
      metadata: {..._getBaseAttributes(), 'paymentSourceId': paymentSourceId},
    );
  }

  Future<bool> depositLeanPaymentSourceDeleteSuccess({
    required String paymentSourceId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourceDeleteSuccess
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourceDeleteSuccess
              .eventName,
      metadata: {..._getBaseAttributes(), 'paymentSourceId': paymentSourceId},
    );
  }

  Future<bool> depositLeanPaymentSourceDeleteError({
    required String paymentSourceId,
    String? errorCode,
    String? errorMessage,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .depositLeanPaymentSourceDeleteError
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.depositLeanPaymentSourceDeleteError.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'paymentSourceId': paymentSourceId,
        if (errorCode != null) 'errorCode': errorCode,
        if (errorMessage != null) 'errorMessage': errorMessage,
      },
    );
  }
}

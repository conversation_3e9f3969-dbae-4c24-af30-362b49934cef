import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:signalr_netcore/hub_connection.dart';
import 'package:socket_client/src/cache/margin_requirements_cache.dart';
import 'package:socket_client/src/cache/orders_cache.dart';
import 'package:socket_client/src/cache/positions_cache.dart';
import 'package:socket_client/src/cache/price_alerts_cache.dart';
import 'package:socket_client/src/cache/quotes_cache.dart';
import 'package:socket_client/src/cache/socket_event_cache_registry.dart';
import 'package:socket_client/src/child_controller.dart';
import 'package:socket_client/src/connection/socket_connection_manager.dart';
import 'package:socket_client/src/event_processing/event_processor_registry.dart';
import 'package:socket_client/src/event_processing/processors/positions/positions_event_processor.dart';
import 'package:socket_client/src/event_type.dart';
import 'package:socket_client/src/interceptor/interceptor_response.dart';
import 'package:socket_client/src/interceptor/socket_interceptor_executor.dart';
import 'package:socket_client/src/socket_client_exception.dart';
import 'package:socket_client/src/socket_connection_options.dart';
import 'package:socket_client/src/socket_event.dart';

class SocketClient {
  SocketClient(
    this.logger, {
    required this.options,
    List<SocketInterceptor> interceptors = const [],
  }) : _socketEventCacheRegistry =
           SocketEventCacheRegistry()
             ..registerCache('quotes', QuotesCache())
             ..registerCache('marginRequirements', MarginRequirementsCache())
             ..registerCache('orders', OrdersCache())
             ..registerCache('positions', PositionsCache())
             ..registerCache('priceAlert', PriceAlertsCache()),
       _interceptorExecutor = SocketInterceptorExecutor([...interceptors]) {
    _connectionManager = SocketConnectionManager(
      logger,
      options,
      _interceptorExecutor,
    );
    _eventProcessorRegistry =
        EventProcessorRegistry()..registerProcessor(
          'positions',
          PositionsEventProcessor(
            logger: logger,
            onEventForward: _handleEventForward,
            isTestMode: options.isTestMode,
          ),
        );
  }

  final SocketEventCacheRegistry _socketEventCacheRegistry;
  final LoggerBase logger;
  final SocketConnectionOptions options;
  late final SocketInterceptorExecutor _interceptorExecutor;
  late final SocketConnectionManager _connectionManager;
  late final EventProcessorRegistry _eventProcessorRegistry;
  final _eventCache = <String, String>{};
  final _stateStreamSubscription =
      <String, StreamSubscription<HubConnectionState>>{};

  TaskEither<Exception, Stream<Object?>> subscribe({
    required String path,
    required EventType eventType,
    List<String> targets = const [],
    List<Object> args = const [],
    bool bypassCache = false,
    required String subscriberId,
  }) {
    return TaskEither.tryCatch(() async {
      final url = _resolveUrl(baseUrl: options.baseUrl, path: path);
      if (url == null) {
        throw SocketClientException(error: 'Invalid url');
      }
      final event = eventType.event;
      var connectionInfo = _connectionManager.getConnectionInfo(url);
      if (connectionInfo == null) {
        _connectionManager.createConnectionInfo(url);
      }
      var existingParentController = _connectionManager.parentStreamController(
        url,
      );
      _listenToHubConnectionState(url);
      if (existingParentController == null) {
        existingParentController = StreamController<Object?>.broadcast(
          onCancel: () => _unsubscribe(url: url, targets: targets),
        );
        _connectionManager.updateConnectionInfo(
          url: url,
          eventStreamController: existingParentController,
        );
      }

      if (eventType is SubscribeEvent) {
        if (existingParentController.hasListener) {
          // If there are already listeners, we need to check for cache misses
          // and manually trigger socket subscription for uncached data
          final cache = _socketEventCacheRegistry.getCacheFor(eventType.event);
          final hasCachedData =
              cache?.has(
                url: url,
                eventType: eventType,
                targets: targets,
                args: args,
              ) ??
              false;

          if (!hasCachedData) {
            // Cache miss: manually trigger socket subscription
            try {
              await _handleSocketEventTransmission(
                url: url,
                eventType: eventType,
                targets: targets,
                args: args,
                updateCache: !bypassCache,
              );
            } catch (error, stackTrace) {
              _handleControllerError(
                existingParentController,
                error,
                stackTrace,
              );
            }
          }
        } else {
          existingParentController.onListen = () async {
            try {
              await _handleSocketEventTransmission(
                url: url,
                eventType: eventType,
                targets: targets,
                args: args,
                updateCache: !bypassCache,
              );
            } catch (error, stackTrace) {
              _handleControllerError(
                existingParentController,
                error,
                stackTrace,
              );
            }
          };
        }
      } else if (eventType is RegisterEvent) {
        await _processRequestWithFallback(
          url: url,
          targets: targets,
          eventType: eventType,
          args: args,
          onInterceptorResolved: (interceptorData) async {
            _handleMockData(
              url,
              eventType,
              targets,
              args,
              interceptorData as Stream,
            );
          },
          onInterceptorNotResolved: () async {
            await _connectionManager.establishConnection(url: url);
          },
        );
      }

      final eventKey = _getCachedEvent(event, subscriberId, targets);
      var existingController =
          _connectionManager.getEventInfo(url, eventKey)?.eventStreamController;
      if (_needsNewController(existingController)) {
        existingController = _createNewController(
          url: url,
          event: event,
          targets: targets,
          subscriberId: subscriberId,
          eventType: eventType,
          args: args,
        );
      }

      final activeSubscription =
          eventType is SubscribeEvent
              ? SubscribeSocketEvent(url, eventType, targets, args)
              : null;

      _connectionManager.updateEventInfo(
        url: url,
        event: eventKey,
        controller: existingController!,
        activeSubscription: activeSubscription,
      );
      return existingController.stream;
    }, (e, s) => SocketClientException(error: e, stackTrace: s));
  }

  Future<void> updateSubscription({
    required String path,
    required EventType eventType,
    List<String> targets = const [],
    List<Object> args = const [],
  }) async {
    final url = _resolveUrl(baseUrl: options.baseUrl, path: path);
    if (url == null) {
      throw SocketClientException(error: 'Invalid url');
    }

    final parentController = _connectionManager.parentStreamController(url);
    if (parentController == null || parentController.isClosed) {
      logger.logInfo('   🚫 Parent controller is closed for $url');
      return;
    }

    return await _createAndProcessSocketEvent(
      url: url,
      eventType: eventType,
      targets: targets,
      args: args,
      forceForward: false,
    );
  }

  /// Handle event forwarding from EventProcessor
  Future<void> _handleEventForward(SocketEvent event) async {
    switch (event) {
      case SubscribeSocketEvent():
        await _subscribeEvent(
          event.url,
          event.eventType,
          event.targets,
          event.args,
        );
        // Update EventInfo for all matching controllers with the new subscription
        _updateEventInfoForSocketEvent(event);
        break;
      case UnsubscribeSocketEvent():
        await _unsubscribeEvent(event.url, event.eventType, event.args);
        // Clear activeSubscription for matching EventInfos
        _updateEventInfoForSocketEvent(event);
        break;
    }
  }

  void _listenToHubConnectionState(String url) {
    try {
      final stateStreamController = _connectionManager.getStateStreamController(
        url,
      );
      if (stateStreamController == null || stateStreamController.isClosed)
        return;
      if (_stateStreamSubscription.containsKey(url)) return;

      final subscription = stateStreamController.stream.listen((state) {
        switch (state) {
          case HubConnectionState.Connected:
            logger.logInfo('   🟢 Connected to $url');
            break;
          case HubConnectionState.Disconnected:
            _handleConnectionClosedWithRecovery();
            logger.logInfo('   🔴 Disconnected from $url');
            break;
          case HubConnectionState.Reconnecting:
            logger.logInfo('   🔄 Reconnecting to $url');
            break;
          case HubConnectionState.Connecting:
            logger.logInfo('   🟡 Connecting to $url');
            break;
          case HubConnectionState.Disconnecting:
            logger.logInfo('   🟠 Disconnecting from $url');
            break;
        }
      });
      _stateStreamSubscription[url] = subscription;
    } catch (e) {
      logger.logInfo('   ❌ Error in _listenToHubConnectionState: $e');
    }
  }

  /// Updates EventInfo for all matching controllers based on the socket event
  /// For SubscribeSocketEvent: sets the activeSubscription
  /// For UnsubscribeSocketEvent: clears the activeSubscription (sets to null)
  void _updateEventInfoForSocketEvent(SocketEvent event) {
    final eventInfos = _connectionManager.getEventInfos(event.url);

    // Determine the activeSubscription value based on event type
    final SubscribeSocketEvent? activeSubscription = switch (event) {
      SubscribeSocketEvent() => event,
      UnsubscribeSocketEvent() => null, // Clear subscription for unsubscribe
    };

    // Find EventInfos that match this event based on event type
    for (final entry in eventInfos.entries) {
      final eventKey = entry.key;

      // Check if this EventInfo corresponds to the same event type
      if (eventKey.contains(event.eventType.event)) {
        final eventInfo = entry.value;
        _connectionManager.updateEventInfo(
          url: event.url,
          event: eventKey,
          controller: eventInfo.eventStreamController,
          activeSubscription: activeSubscription,
        );
      }
    }
  }

  Future<void> dispose() async {
    _eventProcessorRegistry.dispose();
    _socketEventCacheRegistry.clearAllCaches();
    await Future.wait(
      _stateStreamSubscription.values.map(
        (subscription) => subscription.cancel(),
      ),
    );
    return await _connectionManager.dispose();
  }

  @visibleForTesting
  Map<String, ConnectionInfo> get connections => _connectionManager.connections;

  String? _resolveUrl({required String baseUrl, required String path}) =>
      Uri.tryParse(baseUrl)?.resolve(path).toString();

  bool _needsNewController(ChildController? controller) {
    return controller == null || !controller.hasListener || controller.isClosed;
  }

  ChildController _createNewController({
    required String url,
    required String event,
    required List<String> targets,
    required String subscriberId,
    required EventType eventType,
    required List<Object> args,
  }) {
    final eventKey = _getCachedEvent(event, subscriberId, targets);

    logger.logInfo('   🆕 Creating new controller for $eventKey');
    try {
      final controller = _createStreamController(
        url,
        eventType,
        targets,
        args,
        subscriberId,
      );

      return controller;
    } catch (e, stackTrace) {
      logger.logInfo('   ❌ Error in _getOrCreateStreamController: $e');
      logger.logInfo('   Stack trace: $stackTrace');
      // _handleControllerError(controller, e, stackTrace);
      rethrow;
    }
  }

  void _handleControllerError(
    StreamController<Object?>? controller,
    Object error,
    StackTrace? stackTrace,
  ) {
    controller?.addError(SocketClientException(error: error), stackTrace);
    controller?.close();
  }

  String _getCachedEvent(
    String event,
    String subscriberId,
    List<String> targets,
  ) => _eventCache.putIfAbsent(
    "${event}/${subscriberId}/${targets.join('/')}",
    () => "${event}/${subscriberId}/${targets.join('/')}",
  );

  ChildController _createStreamController(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
    String subscriberId,
  ) {
    return ChildController(
      parentStream: _connectionManager.parentStreamController(url)!.stream,
      url: url,
      eventType: eventType,
      targets: targets,
      args: args,
      logger: logger,
      subscriberId: subscriberId,
      onEmitCachedData: (StreamController<Object?> controller) {
        if (eventType is SubscribeEvent) {
          _emitCachedDataIfAvailable(
            url,
            eventType,
            targets,
            args,
            targetController: controller,
          );
        }
      },
    );
  }

  /// Helper method to process interceptor requests and handle the response
  /// with custom callbacks for resolved and unresolved cases.
  Future<void> _processRequestWithFallback({
    required String url,
    required List<String> targets,
    required EventType eventType,
    required List<Object> args,
    Future<void> Function(Object? interceptorData)? onInterceptorResolved,
    Future<void> Function()? onInterceptorNotResolved,
  }) async {
    final interceptorResponse = await _interceptorExecutor.processRequest(
      url: url,
      targets: targets,
      eventType: eventType,
      args: args,
    );

    if (interceptorResponse.resolved) {
      if (onInterceptorResolved != null) {
        await onInterceptorResolved(interceptorResponse.response?.data);
      }
    } else {
      if (onInterceptorNotResolved != null) {
        await onInterceptorNotResolved();
      }
    }
  }

  Future<void> _handleSocketEventTransmission({
    required String url,
    required EventType eventType,
    required List<String> targets,
    required List<Object> args,
    bool updateCache = true,
  }) async {
    return await _processRequestWithFallback(
      url: url,
      targets: targets,
      eventType: eventType,
      args: args,
      onInterceptorResolved: (interceptorData) async {
        await _emitInitialData(
          url,
          eventType,
          targets,
          args,
          interceptorData,
          updateCache: updateCache,
        );
        _subscribeToTarget(url, eventType, targets, args, updateCache);
      },
      onInterceptorNotResolved: () async {
        await _emitInitialData(
          url,
          eventType,
          targets,
          args,
          null,
          updateCache: updateCache,
        );
        _subscribeToTarget(url, eventType, targets, args, updateCache);
      },
    );
  }

  Future<void> _subscribeEvent(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
  ) async {
    return await _processRequestWithFallback(
      url: url,
      targets: targets,
      eventType: eventType,
      args: args,
      onInterceptorResolved: (interceptorData) async {
        await _emitInitialData(url, eventType, targets, args, interceptorData);
        _subscribeToTarget(url, eventType, targets, args, true);
      },
      onInterceptorNotResolved: () async {
        await _emitInitialData(url, eventType, targets, args, null);
        _subscribeToTarget(url, eventType, targets, args, true);
      },
    );
  }

  Future<void> _unsubscribeEvent(
    String url,
    EventType eventType,
    List<Object> args,
  ) async {
    return await _processRequestWithFallback(
      url: url,
      targets: [],
      eventType: eventType,
      args: args,
      onInterceptorNotResolved: () async {
        final hubConnection = _connectionManager.getConnection(url);
        if (_connectionManager.isConnected(hubConnection)) {
          await hubConnection?.invoke(eventType.methodName!, args: args);
          logger.logInfo(
            '   🔄 Unsubscribing from ${eventType.methodName ?? 'null'} and $args',
          );
        }
      },
    );
  }

  void _unsubscribe({required String url, required List<String> targets}) {
    logger.logInfo('\n🔴 [SocketClient] _unsubscribe');
    logger.logInfo('   📌 URL: $url, Targets: $targets');

    final eventInfos = _connectionManager.getEventInfos(url);
    final connection = _connectionManager.getConnection(url);

    logger.logInfo('   ℹ️ Event info exists: ${eventInfos.isNotEmpty}');
    logger.logInfo('   ℹ️ Connection exists: ${connection != null}');

    final streamControllers = _connectionManager.getStreamControllersBy(url);
    logger.logInfo(
      '   📊 Total active controllers for URL: ${streamControllers.length}',
    );

    final hasClosed = streamControllers.every(
      (controller) => controller.isClosed,
    );

    logger.logInfo('   👥 All controller are closed: $hasClosed');

    if (hasClosed) {
      logger.logInfo('   🔄 No active controllers, cleaning up...');
      for (final target in targets) {
        logger.logInfo('   🎯 Removing target handler: $target');
        connection?.off(target);
      }
      logger.logInfo(
        '   🚮 Closing ${streamControllers.length} stream controllers',
      );
      _connectionManager.removeEventInfos(url);
    }

    if (!_connectionManager.isConnected(connection)) {
      logger.logInfo('   🔌 Disconnecting from URL: $url');
      final parentStreamController = _connectionManager.parentStreamController(
        url,
      );
      parentStreamController?.close().whenComplete(() {
        _connectionManager.removeConnection(url);
      });
    }
  }

  Future<void> _emitInitialData(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
    Object? data, {
    bool updateCache = true,
  }) async {
    if (data is Stream) {
      _handleMockData(url, eventType, targets, args, data);
      return;
    }

    final hubConnection = await _connectionManager.establishConnection(
      url: url,
    );
    logger.logInfo(
      '   🔄 Invoking ${eventType.methodName ?? 'null'} with $args',
    );

    if (!_connectionManager.isConnected(hubConnection)) {
      final parentController = _connectionManager.parentStreamController(url);
      parentController?.addError(
        SocketClientException(error: 'Connection not established'),
        StackTrace.current,
      );
      return;
    }
    try {
      final initialData = await hubConnection.invoke(
        eventType.methodName!,
        args: args.isNotEmpty ? args : null,
      );

      if (initialData is List || initialData is Map) {
        final List<Object?> dataToProcess =
            initialData is Map ? [initialData] : initialData as List<Object?>;

        await _handleInitialData(
          url,
          eventType,
          targets,
          args,
          dataToProcess,
          updateCache,
        );
      }
    } catch (e) {
      logger.logError(
        '   ❌ Error invoking ${eventType.methodName ?? 'null'}: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  void _handleMockData(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
    Stream<Object?> data,
  ) {
    final parentController = _connectionManager.parentStreamController(url);

    final subscription = data.listen((streamData) {
      Future.microtask(() async {
        if (parentController != null && !parentController.isClosed) {
          final processedData = await _interceptorExecutor.processResponse(
            url: url,
            eventType: eventType,
            targets: targets,
            args: args,
            data: streamData,
          );
          if (processedData == null) {
            parentController.add(processedData);
            return;
          }
          final payload = <String, dynamic>{"arguments": processedData};
          if (targets.length == 1) {
            payload["target"] = targets.firstOrNull;
          }

          parentController.add(payload);
        }
      });
    });

    subscription.onDone(() => subscription.cancel());
    parentController?.onCancel = () => subscription.cancel();
  }

  Future<void> _handleInitialData(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
    List<Object?> data,
    bool updateCache,
  ) async {
    final controller = _connectionManager.parentStreamController(url);

    if (controller == null || controller.isClosed) return;
    final processedData = await _interceptorExecutor.processResponse(
      url: url,
      eventType: eventType,
      targets: targets,
      args: args,
      data: data,
    );
    if (processedData is List<Object?>) {
      final cache = _socketEventCacheRegistry.getCacheFor(eventType.event);

      if (processedData.isEmpty) {
        // Cache the empty state before emitting null
        if (updateCache) {
          cache?.set(
            url: url,
            eventType: eventType,
            targets: targets,
            args: args,
            data: <Map<String, dynamic>>[], // Empty list to indicate "no data"
          );
        }
        controller.add(null);
        return;
      }

      final targetsLength = targets.length;
      processedData.forEach((Object? item) {
        final payload = {"arguments": item};
        if (targetsLength == 1) {
          payload["target"] = targets.firstOrNull;
        } else {
          payload["target"] = null;
        }
        if (updateCache) {
          cache?.set(
            url: url,
            eventType: eventType,
            targets: targets,
            args: args,
            data: payload,
          );
        }
        controller.add(payload);
      });
    }
  }

  void _subscribeToTarget(
    String url,
    EventType eventType,
    List<String> targets,
    List<Object> args,
    bool updateCache,
  ) {
    final connection = _connectionManager.getConnection(url);
    final controller = _connectionManager.parentStreamController(url);
    final cache = _socketEventCacheRegistry.getCacheFor(eventType.event);

    // connection?.on('LogOff', (data) {
    //   logger.logDebug('LogOff: $data');
    // });

    for (final target in targets) {
      connection?.on(target, (data) {
        if (controller == null || controller.isClosed) return;
        final payload = (data as List).firstOrNull;
        _interceptorExecutor
            .processResponse(
              url: url,
              eventType: eventType,
              targets: targets,
              args: args,
              data: payload,
            )
            .then((processedData) {
              final targetData = {'target': target, 'arguments': processedData};
              controller.add(targetData);
              if (updateCache)
                cache?.set(
                  url: url,
                  eventType: eventType,
                  targets: targets,
                  args: args,
                  data: targetData,
                );
            });
      });
    }
  }

  /// Handles connection closed event with recovery and subscription restoration.
  /// This method is called from the onclose callback and runs the recovery
  /// operation in the background without blocking the callback.
  void _handleConnectionClosedWithRecovery() {
    // Check if recovery is already in progress before starting background recovery
    if (_connectionManager.isRecoveryInProgress) {
      logger.logDebug(
        'Connection Recovery already in progress, skipping subscription recovery',
      );
      return;
    }
    // Run recovery in background to avoid blocking the onclose callback
    _performRecoveryInBackground();
  }

  /// Performs the actual recovery operation in the background.
  Future<void> _performRecoveryInBackground() async {
    try {
      // Use the connection manager's recovery method
      await _connectionManager.handleConnectionClosedWithRecovery();

      final allActiveSubscriptions =
          _connectionManager.getAllActiveSubscriptions();
      if (allActiveSubscriptions.isEmpty) {
        return;
      }
      logger.logInfo(
        '   🔄 Restoring ${allActiveSubscriptions.keys} subscriptions after recovery',
      );

      for (final activeSubscription in allActiveSubscriptions.entries) {
        final url = activeSubscription.key;
        final activeSubscriptions = activeSubscription.value;
        final connection = _connectionManager.getConnection(url);

        if (!_connectionManager.isConnected(connection)) {
          logger.logInfo(
            '   🔌 Connection is not connected after recovery for $url, skipping recovery',
          );
          final parentStreamController = _connectionManager
              .parentStreamController(url);
          parentStreamController?.addError(
            SocketClientException(error: 'Connection not established'),
            StackTrace.current,
          );
          continue; // Skip this URL, continue with others
        }

        // Restore subscriptions using extracted socket event creation method
        var successCount = 0;
        for (final subscription in activeSubscriptions) {
          try {
            await _createAndProcessSocketEvent(
              url: subscription.url,
              eventType: subscription.eventType,
              targets: subscription.targets,
              args: subscription.args,
              forceForward: true,
            );
            successCount++;
          } catch (e) {
            logger.logWarning(
              'Failed to restore subscription $subscription: $e',
            );
            // Propagate error to parent stream controller
            final parentStreamController = _connectionManager
                .parentStreamController(subscription.url);
            parentStreamController?.addError(e, StackTrace.current);
          }
        }

        logger.logInfo(
          'Recovery completed for $url: $successCount/${activeSubscriptions.length} subscriptions restored',
        );
      }
    } catch (e) {
      logger.logWarning('Background recovery failed: $e');
      // Propagate error to all parent stream controllers
      final allActiveSubscriptions =
          _connectionManager.getAllActiveSubscriptions();
      for (final entry in allActiveSubscriptions.entries) {
        final url = entry.key;
        final parentStreamController = _connectionManager
            .parentStreamController(url);
        parentStreamController?.addError(e, StackTrace.current);
      }
    }
  }

  /// Creates a socket event and processes it through the EventProcessor.
  /// Handles RegisterEvent separately as it doesn't go through the EventProcessor.
  Future<void> _createAndProcessSocketEvent({
    required String url,
    required EventType eventType,
    required List<String> targets,
    required List<Object> args,
    bool forceForward = false,
  }) async {
    // Create socket event and process through EventProcessor
    final socketEvent = switch (eventType) {
      SubscribeEvent() => SubscribeSocketEvent(url, eventType, targets, args),
      UnsubscribeEvent() => UnsubscribeSocketEvent(url, eventType, args),
      RegisterEvent() =>
        throw SocketClientException(
          error: 'RegisterEvent should not be processed through EventProcessor',
        ),
    };

    // Handle RegisterEvent directly (not processed through EventProcessor)
    if (eventType is RegisterEvent) {
      return _processRequestWithFallback(
        url: url,
        targets: [],
        eventType: eventType,
        args: args,
        onInterceptorNotResolved: () async {
          await _connectionManager.establishConnection(url: url);
        },
      );
    } else if (eventType is SubscribeEvent) {
      _emitCachedDataIfAvailable(url, eventType, targets, args);
    }

    // Process through EventProcessorRegistry only if a processor is registered for this event type
    if (_eventProcessorRegistry.hasProcessorFor(eventType.event)) {
      final processor = _eventProcessorRegistry.getProcessorFor(
        eventType.event,
      );
      return await processor.processEvent(
        socketEvent,
        forceForward: forceForward,
      );
    }
    // No processor registered for this event type, proceed with direct socket transmission
    return await _handleEventForward(socketEvent);
  }

  /// Emit cached data if available for the subscription
  void _emitCachedDataIfAvailable(
    String url,
    SubscribeEvent eventType,
    List<String> targets,
    List<Object> args, {
    StreamController<Object?>? targetController,
  }) {
    final cache = _socketEventCacheRegistry.getCacheFor(eventType.event);
    if (cache == null) return;

    final hasCachedData = cache.has(
      url: url,
      eventType: eventType,
      targets: targets,
      args: args,
    );

    if (hasCachedData) {
      logger.logDebug(
        '📦 Emitting cached data for ${eventType.event} to satisfy new subscriber',
      );

      // Use provided controller or fallback to parent controller
      final controller =
          targetController ?? _connectionManager.parentStreamController(url);
      if (controller != null && !controller.isClosed) {
        final cachedData = cache.get(
          url: url,
          eventType: eventType,
          targets: targets,
          args: args,
        );

        if (cachedData != null) {
          if (cachedData is List) {
            if (cachedData.isEmpty) {
              // Emit null for empty state (indicating "no data")
              controller.add(null);
            } else {
              for (final item in cachedData) {
                controller.add(item);
              }
            }
          } else {
            controller.add(cachedData);
          }
        }
      }
    }
  }
}

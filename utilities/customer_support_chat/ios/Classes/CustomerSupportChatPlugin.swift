import Flutter
import UIKit
import SM<PERSON><PERSON><PERSON>ore // For the core classes
import SMIClientUI

class PrePopulatedPreChatProvider : HiddenPreChatDelegate {
    
    var preFilledValues = [String: String]()
    func setPreFillValues(_ preFillValues: [String: String]) {
        preFilledValues = preFillValues
    }
    
    func core(_ core: CoreClient, conversation: Conversation, didRequestPrechatValues hiddenPreChatFields: [HiddenPreChatField]) async -> [HiddenPreChatField] {
        
        for field in hiddenPreChatFields {
            field.value = preFilledValues[field.name] ?? ""
        }
        
        return hiddenPreChatFields
    }
}

@available(iOS 14.1, *)
public class CustomerSupportChatPlugin: NSObject, FlutterPlugin {
    let preChat = PrePopulatedPreChatProvider();
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "customer_support_chat", binaryMessenger: registrar.messenger())
    let instance = CustomerSupportChatPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  func viewController(with window: UIWindow?) -> UIViewController? {
    var windowToUse = window
    if windowToUse == nil {
        for window in UIApplication.shared.windows {
            if window.isKeyWindow {
                windowToUse = window
                break
            }
        }
    }
    
    var topController = windowToUse?.rootViewController
    while ((topController?.presentedViewController) != nil) {
        topController = topController?.presentedViewController
    }
    return topController
}

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    let arguments = call.arguments as? [String: Any];
    let developerName = arguments?["developerName"] as? String;
    let organizationId = arguments?["organizationId"] as? String;
    let urlString = arguments?["url"] as? String;
    let prefillData = arguments?["prefillData"] as? [String: String];
    let locale = arguments?["locale"] as? String;
    let theme = arguments?["theme"] as? String;
    
    guard let developerName = developerName,
          let organizationId = organizationId,
          let urlString2 = urlString,
          let url = URL(string: urlString2),
          let prefillData = prefillData,
          let locale = locale,
          let theme = theme else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
        result(false)
        return
    }
    
    switch call.method {
    case "openChat":
        let viewController = start(developerName: developerName, organizationId: organizationId, url: url, prefillData: prefillData, locale: locale, theme: theme)
    let rootViewController = self.viewController(with: nil)
    rootViewController?.present(viewController, animated:true, completion:nil)
    result(true)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

    public func start(developerName: String, organizationId: String, url: URL, prefillData: [String: String], locale: String, theme: String) -> UIViewController {
        
        preChat.setPreFillValues(prefillData)
        
        let conversationID = UUID()
        let coreConfig = SMIClientCore.Configuration(serviceAPI: url, organizationId: organizationId, developerName: developerName)
        let config = UIConfiguration(configuration: coreConfig, conversationId: conversationID, remoteLocaleMap: ["default": locale])
        let coreFactory = CoreFactory.create(withConfig: config)
        coreFactory.setPreChatDelegate(delegate: preChat, queue: .main)
        
        let vc = ModalInterfaceViewController(config)
        vc.modalPresentationStyle = .overFullScreen
        if theme == "dark" {
            vc.overrideUserInterfaceStyle = .dark
        } else {
            vc.overrideUserInterfaceStyle = .light
        }
        
        return vc
    }
}

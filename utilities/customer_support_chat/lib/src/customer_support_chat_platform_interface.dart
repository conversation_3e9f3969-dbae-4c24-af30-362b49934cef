import 'customer_chat_support_config.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'customer_support_chat_method_channel.dart';

abstract class CustomerSupportChatPlatformInterface extends PlatformInterface {
  /// Constructs a CustomerSupportChatPlatform.
  CustomerSupportChatPlatformInterface() : super(token: _token);

  static final Object _token = Object();

  static CustomerSupportChatPlatformInterface _instance =
      CustomerSupportChatMethodChannel();

  /// The default instance of [CustomerSupportChatPlatformInterface] to use.
  ///
  /// Defaults to [CustomerSupportChatMethodChannel].
  static CustomerSupportChatPlatformInterface get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [CustomerSupportChatPlatformInterface] when
  /// they register themselves.
  static set instance(CustomerSupportChatPlatformInterface chatInstance) {
    PlatformInterface.verifyToken(chatInstance, _token);
    _instance = chatInstance;
  }

  Future<bool?> openChat({
    required CustomerChatSupportConfig config,
    required Map<String, String> prefillData,
    required String locale,
    required String theme,
  }) {
    throw UnimplementedError('openChat() has not been implemented.');
  }
}

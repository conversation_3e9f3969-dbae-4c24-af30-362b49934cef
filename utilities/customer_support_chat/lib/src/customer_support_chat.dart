import 'customer_chat_support_config.dart';
import 'customer_support_chat_platform_interface.dart';

class CustomerSupportChat {
  final CustomerChatSupportConfig config;
  const CustomerSupportChat(this.config);

  Future<bool?> openChat({
    required String locale,
    required bool isDarkMode,
    required String brokerCode,
    required String brokerName,
    required String clientAccountExternalId,
  }) {
    return CustomerSupportChatPlatformInterface.instance.openChat(
      config: config,
      prefillData: {
        "brokerCode": brokerCode,
        "brokerName": brokerName,
        "ClientAccountExternalId": clientAccountExternalId,
      },
      locale: locale,
      theme: isDarkMode ? "dark" : "light",
    );
  }
}

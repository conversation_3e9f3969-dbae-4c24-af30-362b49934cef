import 'customer_chat_support_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'customer_support_chat_platform_interface.dart';

/// An implementation of [CustomerSupportChatPlatformInterface] that uses method channels.
class CustomerSupportChatMethodChannel
    extends CustomerSupportChatPlatformInterface {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('customer_support_chat');

  @override
  Future<bool?> openChat({
    required CustomerChatSupportConfig config,
    required Map<String, String> prefillData,
    required String locale,
    required String theme,
  }) async {
    final version = await methodChannel.invokeMethod<bool>('openChat', {
      "developerName": config.developerName,
      "organizationId": config.organizationId,
      "url": config.url,
      "prefillData": prefillData,
      "locale": locale,
      "theme": theme,
    });
    return version;
  }
}
